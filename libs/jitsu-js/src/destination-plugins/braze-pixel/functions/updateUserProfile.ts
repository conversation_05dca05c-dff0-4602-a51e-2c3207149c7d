import { AnalyticsClientEvent } from "@jitsu/protocols/analytics";
import { omit } from "./utils";

export const genders: Record<string, string[]> = {
  M: ["man", "male", "m"],
  F: ["woman", "female", "w", "f"],
  O: ["other", "o"],
  N: ["not applicable", "n"],
  P: ["prefer not to say", "p"],
};

export function toBrazeGender(gender): string | null | undefined {
  if (!gender) {
    return gender;
  }

  const brazeGender = Object.keys(genders).find(key => genders[key].includes(gender.toLowerCase()));
  return brazeGender || gender;
}

export const updateUserProfile = (payload: AnalyticsClientEvent) => {
  if (payload.userId) {
    window.braze.changeUser(payload.userId);
  }

  const user = window.braze.getUser();

  if (!user) return;

  const geo = payload.context.geo || ({} as any);

  geo?.country?.name !== undefined && user.setCountry(geo?.country?.name);
  const traits = payload.traits || ({} as any);

  const reservedFields = ["firstName", "lastName", "avatar", "gender", "user_alias", "braze_id"];

  const customAtributes = omit(traits, reservedFields);

  Object.entries(customAtributes).forEach(([key, value]) => {
    user.setCustomUserAttribute(key, value);
  });

  if (traits.birthday !== undefined) {
    if (!traits.birthday) {
      user.setDateOfBirth(null, null, null);
    } else {
      const date = new Date(String(traits.birthday));
      if (!isNaN(date.getTime())) {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        const day = date.getDate().toString().padStart(2, "0");
        user.setDateOfBirth(year, month, day);
      }
    }
  }

  const email = payload?.properties?.email || payload?.context?.traits?.email || payload?.traits?.email;
  const firstName =
    payload?.properties?.first_name || payload?.context?.traits?.firstName || payload?.traits?.firstName;
  const lastName = payload?.properties?.last_name || payload?.context?.traits?.lastName || payload?.traits?.lastName;
  const phone = payload.properties?.phone || payload.context?.traits?.phone || payload.traits?.phone;
  const gender = toBrazeGender(traits.gender);
  const homeCity = traits.address?.city;
  const latitude = payload.context.geo.location.latitude;
  const longitude = payload.context.geo.location.longitude;
  const locale = payload.context.locale;
  const pushSubscribe = payload.properties?.pushSubscribe || payload.context?.traits?.pushSubscribe;
  const emailSubscribe = payload.properties?.emailSubscribe || payload.context?.traits?.emailSubscribe;

  const subscriptionGroups =
    payload.properties?.subscription_groups || (payload.context?.traits?.subscription_groups as any[]);

  email !== undefined && user.setEmail(email);
  firstName !== undefined && user.setFirstName(firstName);
  lastName !== undefined && user.setFirstName(lastName);
  latitude !== undefined && longitude && user.setLastKnownLocation(latitude, longitude);
  gender !== undefined && user.setGender(gender);
  homeCity !== undefined && user.setHomeCity(homeCity);
  phone !== undefined && user.setPhoneNumber(phone);
  locale !== undefined && user.setLanguage(locale);
  pushSubscribe !== undefined && user.setPushNotificationSubscriptionType(pushSubscribe);
  emailSubscribe !== undefined && user.setEmailNotificationSubscriptionType(emailSubscribe);

  if (Array.isArray(subscriptionGroups)) {
    subscriptionGroups.forEach(group => {
      if (group && group.subscription_group_id && group.subscription_group_state) {
        if (group.subscription_group_state === "subscribed") {
          user.getUser()?.addToSubscriptionGroup(group.subscription_group_id);
        }
        if (group.subscription_group_state === "unsubscribed") {
          user.getUser()?.removeFromSubscriptionGroup(group.subscription_group_id);
        }
      }
    });
  }
};
