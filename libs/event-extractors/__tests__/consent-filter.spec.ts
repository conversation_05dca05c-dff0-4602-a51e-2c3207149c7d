import { shouldProcessEvent } from "../src/consent-filter";
import type { AnalyticsClientEvent } from "@jitsu/protocols/analytics";

describe("consent-filter", () => {
  describe("shouldProcessEvent", () => {
    // Mock event with no consent information
    const eventWithoutConsent: AnalyticsClientEvent = {
      type: "track",
      event: "test_event",
      messageId: "msg-123",
      anonymousId: "anon-123",
      context: {},
    };

    // Mock event with consent information
    const eventWithConsent: AnalyticsClientEvent = {
      type: "track",
      event: "test_event",
      messageId: "msg-123",
      anonymousId: "anon-123",
      context: {
        consent: {
          categoryPreferences: {
            analytics: true,
            marketing: false,
            functional: true,
          },
        },
      },
    };

    it("should return true when no consent information is available", () => {
      expect(shouldProcessEvent(eventWithoutConsent, ["analytics"])).toBe(true);
    });

    it("should return true when no required categories are specified", () => {
      expect(shouldProcessEvent(eventWithConsent, [])).toBe(true);
    });

    it("should return true when all required categories are accepted", () => {
      expect(shouldProcessEvent(eventWithConsent, ["analytics"])).toBe(true);
      expect(shouldProcessEvent(eventWithConsent, ["analytics", "functional"])).toBe(true);
    });

    it("should return false when any required category is not accepted", () => {
      expect(shouldProcessEvent(eventWithConsent, ["marketing"])).toBe(false);
      expect(shouldProcessEvent(eventWithConsent, ["analytics", "marketing"])).toBe(false);
    });

    it("should return false when required category is not in user preferences", () => {
      expect(shouldProcessEvent(eventWithConsent, ["unknown_category"])).toBe(false);
    });

    it("should handle undefined requiredCategories", () => {
      expect(shouldProcessEvent(eventWithConsent, undefined)).toBe(true);
    });

    it("should handle null requiredCategories", () => {
      expect(shouldProcessEvent(eventWithConsent, null)).toBe(true);
    });
  });
});
