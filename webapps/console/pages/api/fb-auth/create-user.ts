import { Api, inferUrl, nextJs<PERSON>pi<PERSON><PERSON><PERSON> } from "../../../lib/api";
import { requireDefined } from "juava";
import { getFirebaseUser, linkFirebaseUser } from "../../../lib/server/firebase-server";
import { getOrCreateUser } from "../../../lib/nextauth.config";

export const api: Api = {
  url: inferUrl(__filename),
  POST: {
    auth: false,
    handle: async ({ req, res }) => {
      const user = requireDefined(await getFirebaseUser(req), `Not authorized`);
      if (!user.internalId) {
        const dbUser = await getOrCreateUser({
          externalId: user.externalId,
          loginProvider: "firebase",
          email: user.email,
          name: user.name || user.email,
          req,
        });
        await linkFirebase<PERSON>ser(user.externalId, dbUser.id);
      } else {
        throw new Error(
          `Firebase user already has internalId (${user.internalId}), this endpoint should not be called`
        );
      }
    },
  },
};

export default nextJsApiHandler(api);
