import type { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { JitsuFunction } from "@jitsu/protocols/functions";
import { RetryError } from "@jitsu/functions-lib";
import { NinetailedCloudCredentials } from "../../meta";
import { reportEvents } from "./reportEvents";

/**
 * Ninetailed cloud mode destination for batch upserting user profiles
 * @see https://developers.ninetailed.co/docs/api-reference/events/batch-upsert-profiles
 */
const NinetailedCloudDestination: JitsuFunction<AnalyticsServerEvent, NinetailedCloudCredentials> = async (
  event,
  ctx
) => {
  const isValidEvent = event.type === "page" || event.type === "track" || event.type === "identify";

  if (isValidEvent) {
    try {
      return await reportEvents(event, ctx);
    } catch (e: any) {
      throw new RetryError(e.message);
    }
  }
};

NinetailedCloudDestination.displayName = "ninetailed-cloud-destination";

NinetailedCloudDestination.description =
  "This function sends events to Ninetail<PERSON> to create and update user profiles for personalization.";

export default NinetailedCloudDestination;
