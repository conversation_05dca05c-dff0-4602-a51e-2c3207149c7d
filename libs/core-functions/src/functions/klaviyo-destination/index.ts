import { RetryError } from "@jitsu/functions-lib";
import type { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { JitsuFunction } from "@jitsu/protocols/functions";
import { KlaviyoCredentials } from "../../meta";
import { ORDER_COMPLETED_EVENT } from "./config";
import { trackEvent } from "./trackEvent";
import { orderCompleted } from "./orderCompleted";
import { upsertProfile } from "./upsertProfile";
import { KLAVIYO_MISSING_IDENTIFIER } from "./functions";
import { ErrorWithCode } from "../../lib/error-types";

// @see https://github.com/segmentio/action-destinations/tree/main/packages/destination-actions/src/destinations/klaviyo
const KlaviyoDestination: JitsuFunction<AnalyticsServerEvent, KlaviyoCredentials> = async (event, ctx) => {
  try {
    if (event.type === "track" && event.event === ORDER_COMPLETED_EVENT) {
      return await orderCompleted(event, ctx);
    } else if (event.type === "track") {
      return await trackEvent(event, ctx);
    } else if (event.type === "identify") {
      return await upsertProfile(event, ctx);
    }
  } catch (error) {
    if (error instanceof ErrorWithCode && error.code === KLAVIYO_MISSING_IDENTIFIER) {
      ctx.log.info(`Skipping event due to ${error.message}`);
      return;
    }

    throw new RetryError(error);
  }
};

KlaviyoDestination.displayName = "klaviyo-destination";

KlaviyoDestination.description = "This function sends events to Klaviyo";

export default KlaviyoDestination;
