diff --git a/dist/destinations/index.js b/dist/destinations/index.js
index 7e7cbc6d8b22627655f37ebac7fc8275a5fc9f63..7d437277580ffff5eb55b202bde2b7dcbcba72fc 100644
--- a/dist/destinations/index.js
+++ b/dist/destinations/index.js
@@ -203,16 +203,25 @@ register('674f2453916dadbd36d899dc', './attentive');
 register('674f23ece330374dc1ecc874', './twilio-messaging');
 register('67be4b2aef865ee6e0484fe5', './amazon-eventbridge');
 register('67e285767bbb94fc090bf3c7', './twilio-messaging-omnichannel');
-function register(id, destinationPath) {
-    const definition = require(destinationPath).default;
-    const resolvedPath = require.resolve(destinationPath);
-    const [directory] = path_1.default.dirname(resolvedPath).split(path_1.default.sep).reverse();
+
+// Changed dynamic imports in favour of static imports as the dynamic imports were causing issues when building.
+// The path to the destination was not being resolved correctly as the module was not being found in the dist folder.
+// You can use destinations by requiring as below.
+const snapConversionsApi = require('./snap-conversions-api').default;
+register('6261a8b6cb4caa70e19116e8', snapConversionsApi);
+const pinterestConversions = require('./pinterest-conversions').default;
+register('6261a8b6cb4caa70e19116e8', pinterestConversions);
+function register(id, destinationModule) {  
+    if (typeof destinationModule === 'string') {
+        return;
+    }
+    const definition = destinationModule;
     exports.manifest[id] = {
         definition,
-        directory,
-        path: resolvedPath
+        directory: "",
+        path: ""
     };
-    exports.destinations[directory] = definition;
+    exports.destinations[definition.slug] = definition;
 }
 async function getDestinationLazy(slug) {
     var _a;
diff --git a/dist/destinations/pinterest-conversions/generated-types.d.ts b/dist/destinations/pinterest-conversions/generated-types.d.ts
index 7b0b708ee5981d4ed83df06b408ed6d3c1c3cad0..914b1453ee1645f186b7fcca89604669210dba09 100644
--- a/dist/destinations/pinterest-conversions/generated-types.d.ts
+++ b/dist/destinations/pinterest-conversions/generated-types.d.ts
@@ -1,4 +1,5 @@
 export interface Settings {
     ad_account_id: string;
     conversion_token: string;
+    test_mode?: boolean;
 }
diff --git a/dist/destinations/pinterest-conversions/index.js b/dist/destinations/pinterest-conversions/index.js
index 12cb39331a00fd62deecc9287f5b822f57386540..30e3301c251a7f2a5cc7e23cc4344de629e74c8b 100644
--- a/dist/destinations/pinterest-conversions/index.js
+++ b/dist/destinations/pinterest-conversions/index.js
@@ -24,6 +24,13 @@ const destination = {
                 description: 'The conversion token for your Pinterest account. This can be found in the Pinterest UI by following the steps mentioned [here](https://developers.pinterest.com/docs/conversions/conversions/#Get%20the%20conversion%20token).',
                 type: 'password',
                 required: true
+            },
+            test_mode: {
+                label: 'Test Mode',
+                description: 'Whether to use test mode. If enabled, the events will be available in the debugger UI.',
+                type: 'boolean',
+                default: false,
+                required: false
             }
         },
         testAuthentication: async (request, { settings }) => {
diff --git a/dist/destinations/pinterest-conversions/pinterest-capi-custom-data.js b/dist/destinations/pinterest-conversions/pinterest-capi-custom-data.js
index 8aa7c65aa595746c1c0fe561320529f09d0fecef..a7052e0de4ed10a93e96ddd586ea7969ef997c2f 100644
--- a/dist/destinations/pinterest-conversions/pinterest-capi-custom-data.js
+++ b/dist/destinations/pinterest-conversions/pinterest-capi-custom-data.js
@@ -14,7 +14,7 @@ exports.custom_data_field = {
         value: {
             label: 'Value',
             description: 'Total value of the event. E.g. if there are multiple items in a checkout event, value should be the total price of all items',
-            type: 'number'
+            type: 'string'
         },
         content_ids: {
             label: 'Content IDs',
@@ -35,7 +35,7 @@ exports.custom_data_field = {
                 },
                 item_price: {
                     label: 'Price',
-                    type: 'number',
+                    type: 'string',
                     description: 'The price of the Item'
                 },
                 quantity: {
diff --git a/dist/destinations/pinterest-conversions/reportConversionEvent/index.js b/dist/destinations/pinterest-conversions/reportConversionEvent/index.js
index e2e8ec7f8d777d64dd2f51118bca827d35514942..b5a636557c61aacec18abda7948b4040cad5a976 100644
--- a/dist/destinations/pinterest-conversions/reportConversionEvent/index.js
+++ b/dist/destinations/pinterest-conversions/reportConversionEvent/index.js
@@ -154,6 +154,11 @@ const action = {
             label: 'Language',
             description: "Two-character ISO-639-1 language code indicating the user's language.",
             type: 'string'
+        },
+        partner_name: {
+            label: 'Partner Name',
+            description: 'Name of the partner.',
+            type: 'string',
         }
     },
     perform: async (request, { settings, payload, features }) => {
@@ -167,7 +172,7 @@ async function processPayload(request, settings, payload, features) {
         throw new actions_core_1.IntegrationError(`User data must contain values for Email or Phone Number or Mobile Ad Identifier or both IP Address and User Agent fields`, 'Misconfigured required field', 400);
     }
     const data = createPinterestPayload(payload, features);
-    return request(`https://api.pinterest.com/${constants_1.API_VERSION}/ad_accounts/${settings.ad_account_id}/events`, {
+    return request(`https://api.pinterest.com/${constants_1.API_VERSION}/ad_accounts/${settings.ad_account_id}/events${settings.test_mode ? '?test=true' : ''}`, {
         method: 'POST',
         json: {
             data: data
@@ -182,12 +187,12 @@ function createPinterestPayload(payload, features) {
             event_time: dayjs_1.default.utc(payload.event_time).unix(),
             event_id: payload.event_id,
             event_source_url: payload.event_source_url,
-            partner_name: constants_1.PARTNER_NAME,
+            partner_name: payload.partner_name,
             opt_out: payload.opt_out,
             user_data: (0, pinterset_capi_user_data_1.hash_user_data)({ user_data: payload.user_data }, features),
             custom_data: {
                 currency: payload?.custom_data?.currency,
-                value: String(payload?.custom_data?.value),
+                value: payload?.custom_data?.value,
                 content_ids: payload.custom_data?.content_ids,
                 contents: payload.custom_data?.contents,
                 num_items: payload.custom_data?.num_items,
diff --git a/dist/destinations/snap-conversions-api/generated-types.d.ts b/dist/destinations/snap-conversions-api/generated-types.d.ts
index 189c658e64533a23fdc6b0d5c7a2836ae4adad04..c73ce1943d2694e1525cfd3064168c6c7dfb34e6 100644
--- a/dist/destinations/snap-conversions-api/generated-types.d.ts
+++ b/dist/destinations/snap-conversions-api/generated-types.d.ts
@@ -2,4 +2,6 @@ export interface Settings {
     pixel_id?: string;
     snap_app_id?: string;
     app_id?: string;
+    access_token?: string;
+    test_mode?: boolean;
 }
diff --git a/dist/destinations/snap-conversions-api/index.js b/dist/destinations/snap-conversions-api/index.js
index 0d119be736bf7d72a9bf0de200af25cc3ab414db..0fbbcf759346177906081945efd2ce1cb61e9ded 100644
--- a/dist/destinations/snap-conversions-api/index.js
+++ b/dist/destinations/snap-conversions-api/index.js
@@ -193,7 +193,20 @@ const destination = {
                 label: 'App ID',
                 description: 'The unique ID assigned for a given application. It should be numeric for iOS, and the human interpretable string for Android. **Required for app events**.',
                 type: 'string'
-            }
+            },
+            access_token: {
+                label: 'Access Token',
+                description: 'The access token for your Snapchat Ad Account. **Required for all events**.',
+                type: 'string',
+                mask: true
+            },
+            test_mode: {
+                label: 'Test Mode',
+                description: "Enable test mode, which appends '/validate' to the endpoint which shows all events in a debug tool on the Snapchat UI.",
+                type: 'boolean',
+                default: false,
+                required: false
+             }
         },
         refreshAccessToken: async (request, { auth }) => {
             const res = await request(ACCESS_TOKEN_URL, {
diff --git a/dist/destinations/snap-conversions-api/reportConversionEvent/snap-capi-v3.js b/dist/destinations/snap-conversions-api/reportConversionEvent/snap-capi-v3.js
index bd4078d6a1555fc903c9f445e57436c95ad870e7..bea22f9fc34a4da8cff4bc5e7c9b6c6666913936 100644
--- a/dist/destinations/snap-conversions-api/reportConversionEvent/snap-capi-v3.js
+++ b/dist/destinations/snap-conversions-api/reportConversionEvent/snap-capi-v3.js
@@ -592,14 +592,13 @@ const buildRequestURL = (settings, action_source, authToken) => {
                 return undefined;
         }
     })());
-    return `https://tr.snapchat.com/v3/${appOrPixelID}/events?access_token=${authToken}`;
+    return `https://tr.snapchat.com/v3/${appOrPixelID}/events${settings?.test_mode ? "/validate" : ""}?access_token=${authToken}`;
 };
 const validatePayload = (payload) => {
     const { action_source, event_name, event_time, custom_data = {}, user_data } = payload;
     (0, utils_1.raiseMisconfiguredRequiredFieldErrorIfNullOrUndefined)(action_source, "The root value is missing the required field 'action_source'.");
     (0, utils_1.raiseMisconfiguredRequiredFieldErrorIfNullOrUndefined)(event_name, "The root value is missing the required field 'event_name'.");
     (0, utils_1.raiseMisconfiguredRequiredFieldErrorIfNullOrUndefined)(event_time, "The root value is missing the required field 'event_time'.");
-    (0, utils_1.raiseMisconfiguredRequiredFieldErrorIf)(!(0, utils_1.isNullOrUndefined)(custom_data.currency) && !CURRENCY_ISO_4217_CODES.has(custom_data.currency), `${custom_data.currency} is not a valid currency code.`);
     (0, utils_1.raiseMisconfiguredRequiredFieldErrorIf)((0, utils_1.isNullOrUndefined)(user_data.em) &&
         (0, utils_1.isNullOrUndefined)(user_data.ph) &&
         (0, utils_1.isNullOrUndefined)(user_data.madid) &&
@@ -610,14 +609,15 @@ const performSnapCAPIv3 = async (request, data, features) => {
     const payloadData = buildPayloadData(payload, settings, features);
     validatePayload(payloadData);
     validateSettingsConfig(settings, payloadData.action_source);
-    const authToken = (0, utils_1.emptyStringToUndefined)(data.auth?.accessToken);
+    const authToken = (0, utils_1.emptyStringToUndefined)(settings?.access_token);
     (0, utils_1.raiseMisconfiguredRequiredFieldErrorIfNullOrUndefined)(authToken, 'Missing valid auth token');
     const url = buildRequestURL(settings, payloadData.action_source, authToken);
     return request(url, {
         method: 'post',
         json: {
             data: [payloadData]
-        }
+        },
+        timeout: 30_000
     });
 };
 exports.performSnapCAPIv3 = performSnapCAPIv3;
