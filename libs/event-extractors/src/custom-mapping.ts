import type { AnalyticsServerEvent, AnalyticsClientEvent } from "@jitsu/protocols/analytics";
import { get, set } from "object-path";

/**
 * Generic event mapping interfaces and types
 */

export interface GenericEventMapping {
  sourceEvent: string;
  targetEvent: string;
  parameters?: Array<{
    sourceProperty: string;
    targetProperty: string;
  }>;
}

export interface ArrayAccessResult {
  __isArrayAccess: true;
  array: any[];
  remainingPath: string;
}

export interface GenericMappingResult {
  targetEvent?: string;
  parameters: Record<string, any>;
  itemMappings?: Array<{
    key: string;
    value: string;
    arrayValue: any;
    remainingPath: string;
  }>;
}

export interface EventMappingOptions {
  matchOnEventName?: boolean; // true for track events (match event.event), false for others (match event.type)
  eventKey?: string; // custom field to match on if needed
}

/**
 * Converts array wildcard syntax from [*] to object-path compatible format
 * and detects if it's a wildcard access
 */
function parseArrayPath(path: string): {
  objectPath: string;
  isWildcard: boolean;
  arrayPath?: string;
  remainingPath?: string;
  arrayIndex?: number;
} {
  // Check for array wildcard pattern like items[*].property
  const wildcardMatch = path.match(/^(.+)\[\*\](.*)$/);
  if (wildcardMatch) {
    const arrayPath = wildcardMatch[1];
    const remaining = wildcardMatch[2].startsWith(".") ? wildcardMatch[2].slice(1) : wildcardMatch[2];
    return {
      objectPath: arrayPath,
      isWildcard: true,
      arrayPath,
      remainingPath: remaining,
    };
  }

  // Check for specific array index pattern like items[0].property
  const indexMatch = path.match(/^(.+)\[(\d+)\](.*)$/);
  if (indexMatch) {
    const arrayPath = indexMatch[1];
    const index = indexMatch[2];
    const remaining = indexMatch[3].startsWith(".") ? indexMatch[3].slice(1) : indexMatch[3];
    // Convert to object-path format: items[0].property -> items.0.property
    const objectPath = remaining ? `${arrayPath}.${index}.${remaining}` : `${arrayPath}.${index}`;
    return {
      objectPath,
      isWildcard: false,
      arrayIndex: parseInt(index, 10),
    };
  }

  return { objectPath: path, isWildcard: false };
}

/**
 * Gets a nested value from an object using dot notation
 * Supports array access with [*] syntax for wildcards and [n] for specific indices
 */
export function getNestedValue(obj: any, path: string): any | ArrayAccessResult {
  if (!obj || typeof obj !== "object") return undefined;

  const { objectPath, isWildcard, arrayPath, remainingPath } = parseArrayPath(path);

  if (isWildcard && arrayPath) {
    // Handle wildcard array access
    const array = get(obj, arrayPath);
    if (Array.isArray(array)) {
      return {
        __isArrayAccess: true,
        array,
        remainingPath: remainingPath || "",
      } as ArrayAccessResult;
    }
    return undefined;
  }

  // Use object-path for regular access (including specific array indices)
  return get(obj, objectPath);
}

/**
 * Sets a value in an object using dot notation path
 * Now uses object-path's set
 */
export function setNestedValue(obj: any, path: string, value: any): void {
  set(obj, path, value);
}

/**
 * Checks if a value is an array access result
 */
export function isArrayAccess(value: any): value is ArrayAccessResult {
  return !!(value && typeof value === "object" && value.__isArrayAccess === true);
}

/**
 * Validates parameter mapping object
 */
function isValidParameterMapping(param: any): param is { sourceProperty: string; targetProperty: string } {
  return (
    typeof param === "object" &&
    param !== null &&
    typeof param.sourceProperty === "string" &&
    typeof param.targetProperty === "string"
  );
}

/**
 * Processes array mappings for items
 * This now works with object-path for setting values
 */
export function processArrayMappings(
  items: any[],
  arrayMappings: Array<{ key: string; value: string; arrayValue: any; remainingPath: string }>
): any[] {
  return items.map((item, index) => {
    const mergedItem = { ...item };

    for (const mapping of arrayMappings) {
      // Get the value from the corresponding index in the array
      if (Array.isArray(mapping.arrayValue) && index < mapping.arrayValue.length) {
        const sourceItem = mapping.arrayValue[index];
        const itemValue = mapping.remainingPath ? get(sourceItem, mapping.remainingPath) : sourceItem;

        if (itemValue !== undefined) {
          set(mergedItem, mapping.value, itemValue);
        }
      }
    }

    return mergedItem;
  });
}

/**
 * Finds and processes ALL matching generic event mappings
 * Returns an array of mapping results for the same source event
 */
export function getGenericEventMappings(
  event: AnalyticsServerEvent | AnalyticsClientEvent,
  mappings?: GenericEventMapping[],
  options: EventMappingOptions = {}
): GenericMappingResult[] | null {
  if (!mappings?.length) return null;

  const { matchOnEventName = true, eventKey } = options;

  // Determine what to match against
  let eventToMatch: string;
  if (eventKey) {
    eventToMatch = getNestedValue(event, eventKey) || "";
  } else if (matchOnEventName && event.type === "track") {
    eventToMatch = event.event || "";
  } else {
    eventToMatch = event.type || "";
  }

  const matchingMappings = mappings.filter(m => m && typeof m === "object" && m.sourceEvent === eventToMatch);

  if (!matchingMappings.length) return null;

  const results: GenericMappingResult[] = [];

  for (const mapping of matchingMappings) {
    const customParameters: Record<string, any> = {};
    const itemMappings: GenericMappingResult["itemMappings"] = [];

    if (mapping.parameters && Array.isArray(mapping.parameters)) {
      for (const param of mapping.parameters) {
        if (!isValidParameterMapping(param)) continue;

        const value = getNestedValue(event, param.sourceProperty);

        if (isArrayAccess(value)) {
          itemMappings.push({
            key: param.sourceProperty,
            value: param.targetProperty,
            arrayValue: value.array,
            remainingPath: value.remainingPath,
          });
        } else if (value !== undefined) {
          customParameters[param.targetProperty] = value;
        }
      }
    }

    results.push({
      targetEvent: mapping.targetEvent || undefined,
      parameters: customParameters,
      itemMappings: itemMappings.length > 0 ? itemMappings : undefined,
    });
  }

  return results;
}

/**
 * Legacy function that returns only the first matching mapping
 * Kept for backward compatibility
 */
export function getGenericEventMapping(
  event: AnalyticsServerEvent | AnalyticsClientEvent,
  mappings?: GenericEventMapping[],
  options: EventMappingOptions = {}
): GenericMappingResult | null {
  const results = getGenericEventMappings(event, mappings, options);
  return results && results.length > 0 ? results[0] : null;
}
