import { AnalyticsClientEvent } from "@jitsu/protocols/analytics";
import { applyFilters, CommonDestinationCredentials, InternalPlugin } from "./index";
import { loadScript } from "../script-loader";

type PinterestTagCredentials = {
  tagId: string;
  useEnhancedMatchLoading?: boolean;
} & CommonDestinationCredentials;

declare global {
  interface Window {
    pintrk?: any;
  }
}

export const pinterestTagPlugin: InternalPlugin<PinterestTagCredentials> = {
  id: "pinterest-tag",
  async handle(config, payload: AnalyticsClientEvent) {
    if (!applyFilters(payload, config, "pinterest-tag")) {
      return;
    }

    await initPinterestPixelIfNeeded(config, payload);

    const { event, properties, type } = payload;

    switch (type) {
      case "page":
        const pinterestPageProps = {
          name: properties.title,
          event_id: payload.messageId,
        };
        window.pintrk("track", "PageVisit", pinterestPageProps);
        break;

      case "identify":
        const email = properties.email || payload.context?.traits?.email || payload.traits?.email;
        window.pintrk("set", { np: "segment", em: email });
        break;

      case "track":
        switch (event) {
          case "Product List Viewed":
            window.pintrk("track", "ViewCategory", {
              name: properties.item_list_name,
              event_id: payload.messageId,
              category: properties.category,
              ...(properties.products && { line_items: createLineItems(properties.products as any[]) }),
            });
            break;
          case "Products Searched":
            window.pintrk("track", "Search", createPropertyMapping(payload, properties));
            break;
          case "Products List Filtered":
            window.pintrk("track", "Search", createPropertyMapping(payload, properties));
            break;
          case "Product Added":
            window.pintrk("track", "AddToCart", createPropertyMapping(payload, properties));
            break;
          case "Order Completed":
            window.pintrk("track", "Checkout", createPropertyMapping(payload, properties));
            break;
          default:
            window.pintrk("track", event, properties);
            break;
        }
    }
  },
};

type PinterestPixelState = "fresh" | "loading" | "loaded" | "failed";

function getPinterestPixelState(): PinterestPixelState {
  return window["__chordCDPPinterestPixelState"] || "fresh";
}

function setPinterestPixelState(s: PinterestPixelState) {
  window["__chordCDPPinterestPixelState"] = s;
}

async function initPinterestPixelIfNeeded(config: PinterestTagCredentials, event: AnalyticsClientEvent): Promise<void> {
  if (getPinterestPixelState() !== "fresh") {
    return;
  }

  setPinterestPixelState("loading");

  await loadScript("https://s.pinimg.com/ct/core.js")
    .then(() => {
      setPinterestPixelState("loaded");

      if (!config.tagId) return;
      if (!window.pintrk) {
        window.pintrk = function () {
          window.pintrk.queue.push(Array.prototype.slice.call(arguments));
        };

        const n = window.pintrk;
        (n.queue = []), (n.version = "3.0");
      }

      const email = (event?.properties?.email || event?.context?.traits?.email || event?.traits?.email) as string;

      if (email && config.useEnhancedMatchLoading) {
        window.pintrk("load", config.tagId, { em: email });
      } else {
        window.pintrk("load", config.tagId);
      }
      window.pintrk("page");
    })
    .catch(e => {
      console.warn(`Pinterest Pixel (pixelId=${config.tagId}) init failed: ${e.message}`, e);
      setPinterestPixelState("failed");
    });
}

function createPropertyMapping(payload: AnalyticsClientEvent, properties: any) {
  return {
    event_id: payload.messageId,
    ...createEventProperties(payload, properties),
    ...(properties.products && { line_items: createLineItems(properties.products as any[]) }),
    // event with single product like Product Added - product properties are at root level
    ...((properties?.sku || properties?.product_id) && {
      line_items: createLineItems([properties] as any[]),
    }),
  };
}

function createEventProperties(payload: AnalyticsClientEvent, properties: any) {
  return {
    event_id: payload?.messageId,
    search_query: properties?.query,
    order_id: properties?.order_id,
    coupon: properties?.coupon,
    value: properties?.value,
    currency: properties?.currency,
  };
}

function createLineItems(products: any[]) {
  return products.map(product => {
    return {
      product_name: product?.name,
      product_id: product?.sku || product?.product_id,
      product_category: product?.category,
      product_variant_id: product?.variant_id,
      product_variant: product?.variant,
      product_price: product?.price,
      product_quantity: product?.quantity,
      product_brand: product?.brand,
    };
  });
}
