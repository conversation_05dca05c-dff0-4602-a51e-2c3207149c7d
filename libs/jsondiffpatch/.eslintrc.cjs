module.exports = {
  root: true,
  extends: ["eslint:recommended", "plugin:@typescript-eslint/recommended-type-checked"],
  plugins: ["@typescript-eslint"],
  parser: "@typescript-eslint/parser",
  parserOptions: {
    project: true,
    tsconfigRootDir: __dirname,
  },
  overrides: [
    {
      files: ["test/**/*.ts"],
      extends: ["eslint:recommended", "plugin:@typescript-eslint/recommended-type-checked"],
      plugins: ["@typescript-eslint"],
      parser: "@typescript-eslint/parser",
      parserOptions: {
        project: "./test/tsconfig.json",
        tsconfigRootDir: __dirname,
      },
    },
  ],
};
