import { testJitsuFunction, TestOptions } from "./lib/testing-lib";
import AttentiveDestination from "../src/functions/attentive-destination";
import { AttentiveCredentials } from "../src/meta";
import { eventsSequence } from "./lib/test-data-chord";

// npx pnpm tsc -p . && TEST_ATTENTIVE_DESTINATION=true ATTENTIVE_API_KEY=changeme npx pnpm jest --verbose -t 'attentive-destination-integration'
test("attentive-destination-integration", async () => {
  if (!process.env.TEST_ATTENTIVE_DESTINATION && !process.env.ATTENTIVE_API_KEY) {
    console.log(
      "Skipping Attentive destination integration test - TEST_ATTENTIVE_DESTINATION or ATTENTIVE_API_KEY is not set"
    );
    return;
  }

  const opts: TestOptions<AttentiveCredentials> = {
    func: AttentiveDestination,
    config: {
      apiKey: process.env.ATTENTIVE_API_KEY || "",
    },
    events: eventsSequence({
      coreEvents: true,
      identifyEvents: false,
      customEvents: false,
      basicEvents: false,
    }),
  };
  await testJitsuFunction(opts);
});
