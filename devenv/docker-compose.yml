version: "3.8"
services:
  jitsu-dev-redis:
    tty: true
    image: redis:6.2-alpine
    restart: always
    ports:
      - "${REDIS_PORT:-6380}:6379"
    command: redis-server --save 20 1 --loglevel warning --requirepass redis-mqf3nzx
    volumes:
      - ./data/redis:/var/lib/redis

  jitsu-dev-redisinsight:
    tty: true
    image: redislabs/redisinsight:latest
    ports:
      - "${RI_PORT:-3011}:8001"
    volumes:
      - ./data/redis-insights:/db

  jitsu-dev-postgres:
    tty: true
    image: postgres:14
    restart: always
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres-mqf3nzx
    logging:
      options:
        max-size: 10m
        max-file: "3"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready", "-d", "postgres", "-U", "postgres"]
      interval: 1s
      timeout: 10s
      retries: 10
    ports:
      - "${PG_PORT:-5438}:5432"
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
  jitsu-dev-kafka:
    image: bitnami/kafka:3.4
    #    ports:
    #      - "19092:19092"
    #      - "19093:19093"
    environment:
      KAFKA_CFG_NODE_ID: 0
      KAFKA_CFG_PROCESS_ROLES: controller,broker
      KAFKA_CFG_LISTENERS: PLAINTEXT://:19093,CONTROLLER://:9093,EXTERNAL://:19092
      KAFKA_CFG_ADVERTISED_LISTENERS: PLAINTEXT://jitsu-dev-kafka:19093,EXTERNAL://localhost:19092
      KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP: CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT,EXTERNAL:PLAINTEXT
      KAFKA_CFG_CONTROLLER_QUORUM_VOTERS: 0@jitsu-dev-kafka:9093
      KAFKA_CFG_CONTROLLER_LISTENER_NAMES: CONTROLLER
      KAFKA_CFG_AUTO_CREATE_TOPICS_ENABLE: true
      ALLOW_PLAINTEXT_LISTENER: yes

  jitsu-dev-kafka-console:
    image: docker.redpanda.com/redpandadata/console:latest
    ports:
      - "${KAFKA_CONSOLE_PORT:-3032}:8080"
    environment:
      KAFKA_BROKERS: "jitsu-dev-kafka:19093"
    depends_on:
      - jitsu-dev-kafka

  bulker-dev:
    tty: true
    image: jitsucom/bulker:latest
    restart: on-failure
    ports:
      - "${BULKER_PORT:-3042}:3042"
    volumes:
      - ./utils:/utils
    environment:
      TERM: "xterm-256color"
      BULKER_KAFKA_BOOTSTRAP_SERVERS: "jitsu-dev-kafka:19093"
      BULKER_DESTINATION_POSTGRES: "{id: 'postgres', type: 'postgres', mode: 'stream', credentials: {host: 'jitsu-dev-postgres', username: 'postgres', password: 'postgres-mqf3nzx', port: 5432, database: 'postgres', parameters: {sslmode: 'disable'}}}"
      BULKER_AUTH_TOKENS: "4ba41958f341469993fd8ea1c0c932f0"
    command: [
        #'chmod', '755', '/utils/wait-for-it.sh', '&&',
        "/utils/wait-for-it.sh",
        "jitsu-dev-kafka:19093",
        "--",
        "/utils/wait-for-it.sh",
        "jitsu-dev-postgres:5432",
        "--",
        "/app/bulkerapp",
      ]
    depends_on:
      - jitsu-dev-postgres
      - jitsu-dev-kafka
  keycloak:
    command: start-dev
    image: "quay.io/keycloak/keycloak:26.0.4"
    environment:
      - KC_BOOTSTRAP_ADMIN_PASSWORD=admin
      - KC_BOOTSTRAP_ADMIN_USERNAME=admin
    ports:
      - "8080:8080"
