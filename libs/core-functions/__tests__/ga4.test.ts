import { testJitsuFunction, TestOptions } from "./lib/testing-lib";
import Ga4Destination from "../src/functions/ga4-destination";
import { Ga4Credentials } from "../src/meta";
import { eventsSequence } from "./lib/test-data-chord";

// npx pnpm tsc -p . && TEST_GA4_DESTINATION=true GA4_API_SECRET=changeme GA4_MEASUREMENT_ID=changeme npx pnpm jest --verbose -t 'ga4-destination-integration'
test("ga4-destination-integration", async () => {
  if (!process.env.TEST_GA4_DESTINATION && !process.env.GA4_API_SECRET && !process.env.GA4_MEASUREMENT_ID) {
    console.log(
      "Skipping Google destination integration test - TEST_GA4_DESTINATION or GA4_API_SECRET or GA4_MEASUREMENT_ID is not set"
    );
    return;
  }

  const opts: TestOptions<Ga4Credentials> = {
    func: Ga4Destination,
    config: {
      apiSecret: process.env.GA4_API_SECRET || "",
      measurementId: process.env.GA4_MEASUREMENT_ID || "",
      url: "https://www.google-analytics.com/mp/collect",
      events: "",
      firebaseAppId: process.env.GA4_FIREBASE_APP_ID || "",
    },
    events: eventsSequence({ coreEvents: true, basicEvents: false, identifyEvents: false, customEvents: true }),
  };
  await testJitsuFunction(opts);
});
