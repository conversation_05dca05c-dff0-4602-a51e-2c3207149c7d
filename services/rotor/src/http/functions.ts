import { getLog, requireDefined } from "juava";
import { IngestMessage } from "@jitsu/protocols/async-request";
import { MessageHandlerContext, rotorMessageHandler } from "../lib/message-handler";
import { CONNECTION_IDS_HEADER } from "../lib/rotor";
import { AnyEvent } from "@jitsu/protocols/functions";
import isEqual from "lodash/isEqual";
import { parse as semverParse } from "semver";
import * as jsondiffpatch from "jsondiffpatch";
import { connectionsStore, functionsStore, streamsStore } from "../lib/repositories";
import { promHandlerMetric } from "../lib/metrics";

const jsondiffpatchInstance = jsondiffpatch.create();
const log = getLog("functions_handler");

export const FunctionsHandler =
  (rotorContext: Omit<MessageHandlerContext, "connectionStore" | "functionsStore" | "streamsStore">) =>
  async (req, res) => {
    const message = req.body as IngestMessage;
    //log.atInfo().log(`Functions handler. Message ID: ${message.messageId} connectionId: ${message.connectionId}`);
    const result = await rotorMessageHandler(message, {
      ...rotorContext,
      connectionStore: requireDefined(connectionsStore.getCurrent(), "Connection store is not initialized"),
      functionsStore: requireDefined(functionsStore.getCurrent(), "Functions store is not initialized"),
      streamsStore: requireDefined(streamsStore.getCurrent(), "Streams store is not initialized"),
    });
    if (result?.events && result.events.length > 0) {
      res.json(result.events);
    } else {
      res.status(204).send();
    }
  };

export const FunctionsHandlerMulti =
  (rotorContext: Omit<MessageHandlerContext, "connectionStore" | "functionsStore" | "streamsStore">) =>
  async (req, res, next) => {
    const connectionIds = (req.query.ids ?? "").split(",") as string[];
    const message = req.body as IngestMessage;
    const functionsFetchTimeout = req.headers["x-request-timeout-ms"]
      ? parseInt(req.headers["x-request-timeout-ms"] as string)
      : 2000;
    const prom = connectionIds
      .filter(id => !!id)
      .map(id => {
        //log.atInfo().log(`Functions handler2. Message ID: ${message.messageId} connectionId: ${id}`);
        return rotorMessageHandler(
          message,
          {
            ...rotorContext,
            connectionStore: requireDefined(connectionsStore.getCurrent(), "Connection store is not initialized"),
            functionsStore: requireDefined(functionsStore.getCurrent(), "Functions store is not initialized"),
            streamsStore: requireDefined(streamsStore.getCurrent(), "Streams store is not initialized"),
          },
          "all",
          { [CONNECTION_IDS_HEADER]: id },
          false,
          0,
          functionsFetchTimeout
        );
      });
    await Promise.all(prom)
      .then(results => {
        connectionIds.forEach((id, i) => {
          promHandlerMetric.inc({ connectionId: id, status: "success" }, 1);
        });
        const events = Object.fromEntries(
          results.map(result => [result?.connectionId, mapDiff(message, result?.events)])
        );
        res.json(events);
      })
      .catch(e => {
        connectionIds.forEach((id, i) => {
          promHandlerMetric.inc({ connectionId: id, status: "error" }, 1);
        });
        next(`[${connectionIds}] Error processing functions: ${e.name}: ${e.message}`);
      });
  };

function mapDiff(message: IngestMessage, newEvents?: AnyEvent[]) {
  if (!newEvents) {
    return [];
  }

  return newEvents.map(e => {
    if (isEqual(message.httpPayload, e)) {
      return "same";
    }
    let supportsDiff = false;
    const library = message.httpPayload?.context?.library;
    if (library?.name === "@jitsu/js") {
      const semver = semverParse(library.version);
      if (semver && semver.major >= 2) {
        supportsDiff = true;
      }
    }
    if (!supportsDiff) {
      return e;
    }

    const originalSize = JSON.stringify(message.httpPayload).length;
    const diff = jsondiffpatchInstance.diff(message.httpPayload, e);
    if (!diff) {
      return "same";
    }
    const diffSize = JSON.stringify(diff).length;
    if (diffSize > originalSize) {
      return e;
    } else {
      return { __diff: diff };
    }
  });
}
