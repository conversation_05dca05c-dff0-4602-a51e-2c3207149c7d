import type { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { validate, parseFql } from "@segment/destination-subscriptions";
import { getDestinationByIdOrKey } from "@segment/action-destinations";
import { FullContext } from "@jitsu/protocols/functions";
import type { SnapConversionsCredentials } from "../../../meta";
import _ from "lodash";
import { customMapping } from "../customMapping";

/**
 * Sends tracking event data to Snapchat Conversions API
 * @param event Analytics event to be processed
 * @param ctx Function execution context
 * @returns Segment Result[] object
 */
export const reportConversionEvent = async (event: AnalyticsServerEvent, ctx: FullContext) => {
  try {
    const props = ctx.props as SnapConversionsCredentials;
    const destination = await getDestinationByIdOrKey("actions-snap-conversions");

    if (!destination) {
      throw new Error("Snapchat destination not found");
    }

    const action = destination.actions["reportConversionEvent"];

    // Find matching preset using Segment's FQL syntax
    const eventPreset =
      destination.definition.presets?.find(
        // This "snap browser plugin" preset looks unrelated to cloud mode conversions as it catches every event type and has no mapping
        preset => preset["name"] !== "Snap Browser Plugin" && validate(parseFql(preset["subscribe"]), event)
      ) ?? null;

    if (!eventPreset) {
      ctx.log.debug("No matching Snapchat preset for event found", { event });
      return [];
    }

    const settings = {
      pixel_id: props.pixelId,
      access_token: props.accessToken,
      test_mode: props?.testMode || false,
    };

    const defaultMapping = eventPreset.mapping || {};

    // Merge default mapping with custom mapping logic
    const mapping = _.merge({}, defaultMapping, customMapping(event));

    // Execute the Snapchat API call
    const response = await action.execute({
      mapping,
      data: event as unknown as { [key: string]: unknown },
      settings,
      auth: { accessToken: "", refreshToken: "" }, // We have patched the segment destination to use the accessToken directly
    });

    ctx.log.info("Snap event tracking successful", { response, event, mapping, customMapping, defaultMapping });
    return response;
  } catch (error) {
    console.debug("Snap event tracking error", { error, event, customMapping: customMapping(event) });
    ctx.log.error("Snap event tracking failed", {
      error,
      event,
      customMapping: customMapping(event),
    });
    throw error;
  }
};
