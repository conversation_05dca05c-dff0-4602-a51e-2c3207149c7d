import {
  getNestedValue,
  setNestedValue,
  isArrayAccess,
  processArrayMappings,
  getGenericEventMapping,
  getGenericEventMappings,
  type GenericEventMapping,
  type ArrayAccessResult,
} from "../src/custom-mapping";
import type { AnalyticsServerEvent, AnalyticsClientEvent } from "@jitsu/protocols/analytics";

describe("custom-mapping", () => {
  describe("getNestedValue", () => {
    const testObject = {
      level1: {
        level2: {
          level3: "value",
          array: [1, 2, 3],
        },
        items: [
          { id: 1, name: "item1" },
          { id: 2, name: "item2" },
        ],
      },
      simple: "simpleValue",
      nullValue: null,
      undefinedValue: undefined,
    };

    it("should get simple property value", () => {
      expect(getNestedValue(testObject, "simple")).toBe("simpleValue");
    });

    it("should get nested property value", () => {
      expect(getNestedValue(testObject, "level1.level2.level3")).toBe("value");
    });

    it("should return undefined for non-existent path", () => {
      expect(getNestedValue(testObject, "level1.nonexistent.path")).toBeUndefined();
    });

    it("should handle null values in path", () => {
      expect(getNestedValue(testObject, "nullValue.something")).toBeUndefined();
    });

    it("should handle undefined values", () => {
      expect(getNestedValue(testObject, "undefinedValue")).toBeUndefined();
    });

    it("should handle array access with [*] syntax", () => {
      const result = getNestedValue(testObject, "level1.items[*].name");
      expect(isArrayAccess(result)).toBe(true);
      expect((result as ArrayAccessResult).array).toEqual([
        { id: 1, name: "item1" },
        { id: 2, name: "item2" },
      ]);
      expect((result as ArrayAccessResult).remainingPath).toBe("name");
    });

    it("should return undefined for array access on non-array", () => {
      expect(getNestedValue(testObject, "level1.level2[*].something")).toBeUndefined();
    });

    it("should handle empty object", () => {
      expect(getNestedValue({}, "any.path")).toBeUndefined();
    });

    it("should handle null object", () => {
      expect(getNestedValue(null, "any.path")).toBeUndefined();
    });

    it("should handle specific array index access", () => {
      const testObject = {
        products: [
          { id: 1, name: "Product 1", currencyFormat: "$" },
          { id: 2, name: "Product 2", currencyFormat: "€" },
        ],
      };

      expect(getNestedValue(testObject, "products[0].currencyFormat")).toBe("$");
      expect(getNestedValue(testObject, "products[1].currencyFormat")).toBe("€");
      expect(getNestedValue(testObject, "products[0].name")).toBe("Product 1");
    });

    it("should return undefined for out of bounds array index", () => {
      const testObject = {
        products: [{ id: 1 }],
      };

      expect(getNestedValue(testObject, "products[5].id")).toBeUndefined();
      expect(getNestedValue(testObject, "products[-1].id")).toBeUndefined();
    });

    it("should handle nested array index access", () => {
      const testObject = {
        level1: {
          items: [{ nested: { value: "first" } }, { nested: { value: "second" } }],
        },
      };

      expect(getNestedValue(testObject, "level1.items[0].nested.value")).toBe("first");
      expect(getNestedValue(testObject, "level1.items[1].nested.value")).toBe("second");
    });
  });

  describe("setNestedValue", () => {
    it("should set simple property value", () => {
      const obj = {};
      setNestedValue(obj, "simple", "value");
      expect(obj).toEqual({ simple: "value" });
    });

    it("should set nested property value", () => {
      const obj = {};
      setNestedValue(obj, "level1.level2.level3", "value");
      expect(obj).toEqual({
        level1: {
          level2: {
            level3: "value",
          },
        },
      });
    });

    it("should overwrite existing value", () => {
      const obj = { level1: { level2: "old" } };
      setNestedValue(obj, "level1.level2", "new");
      expect(obj).toEqual({ level1: { level2: "new" } });
    });

    it("should create nested structure in existing object", () => {
      const obj = { existing: "value" };
      setNestedValue(obj, "new.nested.path", "value");
      expect(obj).toEqual({
        existing: "value",
        new: {
          nested: {
            path: "value",
          },
        },
      });
    });
  });

  describe("isArrayAccess", () => {
    it("should return true for array access result", () => {
      const arrayAccess: ArrayAccessResult = {
        __isArrayAccess: true,
        array: [1, 2, 3],
        remainingPath: "property",
      };
      expect(isArrayAccess(arrayAccess)).toBe(true);
    });

    it("should return false for regular objects", () => {
      expect(isArrayAccess({ array: [1, 2, 3] })).toBe(false);
      expect(isArrayAccess({ __isArrayAccess: false })).toBe(false);
    });

    it("should return false for null and undefined", () => {
      expect(isArrayAccess(null)).toBe(false);
      expect(isArrayAccess(undefined)).toBe(false);
    });
  });

  describe("processArrayMappings", () => {
    it("should process array mappings correctly", () => {
      const items = [{ id: 1 }, { id: 2 }];
      const arrayMappings = [
        {
          key: "products[*].name",
          value: "productName",
          arrayValue: [{ name: "Product 1" }, { name: "Product 2" }],
          remainingPath: "name",
        },
      ];

      const result = processArrayMappings(items, arrayMappings);
      expect(result).toEqual([
        { id: 1, productName: "Product 1" },
        { id: 2, productName: "Product 2" },
      ]);
    });

    it("should handle nested remaining paths", () => {
      const items = [{ id: 1 }, { id: 2 }];
      const arrayMappings = [
        {
          key: "products[*].details.name",
          value: "product.name",
          arrayValue: [{ details: { name: "Product 1" } }, { details: { name: "Product 2" } }],
          remainingPath: "details.name",
        },
      ];

      const result = processArrayMappings(items, arrayMappings);
      expect(result).toEqual([
        { id: 1, product: { name: "Product 1" } },
        { id: 2, product: { name: "Product 2" } },
      ]);
    });

    it("should handle missing array values", () => {
      const items = [{ id: 1 }, { id: 2 }, { id: 3 }];
      const arrayMappings = [
        {
          key: "products[*].name",
          value: "productName",
          arrayValue: [{ name: "Product 1" }], // Only one item
          remainingPath: "name",
        },
      ];

      const result = processArrayMappings(items, arrayMappings);
      expect(result).toEqual([{ id: 1, productName: "Product 1" }, { id: 2 }, { id: 3 }]);
    });

    it("should handle empty remaining path", () => {
      const items = [{ id: 1 }, { id: 2 }];
      const arrayMappings = [
        {
          key: "values[*]",
          value: "value",
          arrayValue: ["Value 1", "Value 2"],
          remainingPath: "",
        },
      ];

      const result = processArrayMappings(items, arrayMappings);
      expect(result).toEqual([
        { id: 1, value: "Value 1" },
        { id: 2, value: "Value 2" },
      ]);
    });
  });

  describe("getGenericEventMapping", () => {
    const mappings: GenericEventMapping[] = [
      {
        sourceEvent: "page_viewed",
        targetEvent: "Page Viewed",
        parameters: [
          { sourceProperty: "properties.url", targetProperty: "page_url" },
          { sourceProperty: "properties.title", targetProperty: "page_title" },
        ],
      },
      {
        sourceEvent: "product_added",
        targetEvent: "Add to Cart",
        parameters: [
          { sourceProperty: "properties.product_id", targetProperty: "product.id" },
          { sourceProperty: "properties.items[*].name", targetProperty: "product.items" },
        ],
      },
    ];

    it("should map track event by event name", () => {
      const event: AnalyticsClientEvent = {
        type: "track",
        event: "page_viewed",
        messageId: "msg-123",
        anonymousId: "anon-123",
        properties: {
          url: "https://example.com",
          title: "Example Page",
        },
        context: {},
      };

      const result = getGenericEventMapping(event, mappings);
      expect(result).toEqual({
        targetEvent: "Page Viewed",
        parameters: {
          page_url: "https://example.com",
          page_title: "Example Page",
        },
        itemMappings: undefined,
      });
    });

    it("should handle array access in parameters", () => {
      const event: AnalyticsClientEvent = {
        type: "track",
        event: "product_added",
        messageId: "msg-123",
        anonymousId: "anon-123",
        properties: {
          product_id: "123",
          items: [{ name: "Item 1" }, { name: "Item 2" }],
        },
        context: {},
      };

      const result = getGenericEventMapping(event, mappings);
      expect(result).toEqual({
        targetEvent: "Add to Cart",
        parameters: {
          "product.id": "123",
        },
        itemMappings: [
          {
            key: "properties.items[*].name",
            value: "product.items",
            arrayValue: [{ name: "Item 1" }, { name: "Item 2" }],
            remainingPath: "name",
          },
        ],
      });
    });

    it("should return null for unmapped events", () => {
      const event: AnalyticsClientEvent = {
        type: "track",
        event: "unmapped_event",
        messageId: "msg-123",
        anonymousId: "anon-123",
        context: {},
      };

      const result = getGenericEventMapping(event, mappings);
      expect(result).toBeNull();
    });

    it("should handle non-track events by type", () => {
      const event: AnalyticsServerEvent = {
        type: "identify",
        messageId: "msg-123",
        anonymousId: "anon-123",
        context: {},
      };

      const identifyMappings: GenericEventMapping[] = [
        {
          sourceEvent: "identify",
          targetEvent: "User Identified",
          parameters: [{ sourceProperty: "traits.email", targetProperty: "email" }],
        },
      ];

      const result = getGenericEventMapping(event, identifyMappings, { matchOnEventName: false });
      expect(result?.targetEvent).toBe("User Identified");
    });

    it("should handle custom event key", () => {
      const event: AnalyticsClientEvent = {
        type: "track",
        event: "ignored",
        messageId: "msg-123",
        anonymousId: "anon-123",
        properties: {
          customEventName: "page_viewed",
        },
        context: {},
      };

      const result = getGenericEventMapping(event, mappings, {
        eventKey: "properties.customEventName",
      });
      expect(result?.targetEvent).toBe("Page Viewed");
    });

    it("should handle missing parameters gracefully", () => {
      const event: AnalyticsClientEvent = {
        type: "track",
        event: "page_viewed",
        messageId: "msg-123",
        anonymousId: "anon-123",
        properties: {
          // Missing url and title
        },
        context: {},
      };

      const result = getGenericEventMapping(event, mappings);
      expect(result).toEqual({
        targetEvent: "Page Viewed",
        parameters: {},
        itemMappings: undefined,
      });
    });

    it("should handle invalid parameter mappings", () => {
      const invalidMappings: GenericEventMapping[] = [
        {
          sourceEvent: "test_event",
          targetEvent: "Test Event",
          parameters: [
            { sourceProperty: "properties.valid", targetProperty: "valid" },
            // @ts-ignore - Testing invalid parameter
            { sourceProperty: "invalid" }, // Missing targetProperty
            // @ts-ignore - Testing invalid parameter
            { targetProperty: "invalid" }, // Missing sourceProperty
            // @ts-ignore - Testing invalid parameter
            "not an object", // Invalid type
            null, // Null parameter
          ] as any,
        },
      ];

      const event: AnalyticsClientEvent = {
        type: "track",
        event: "test_event",
        messageId: "msg-123",
        anonymousId: "anon-123",
        properties: {
          valid: "value",
        },
        context: {},
      };

      const result = getGenericEventMapping(event, invalidMappings);
      expect(result).toEqual({
        targetEvent: "Test Event",
        parameters: {
          valid: "value",
        },
        itemMappings: undefined,
      });
    });

    it("should return null for empty mappings", () => {
      const event: AnalyticsClientEvent = {
        type: "track",
        event: "any_event",
        messageId: "msg-123",
        anonymousId: "anon-123",
        context: {},
      };

      expect(getGenericEventMapping(event, [])).toBeNull();
      expect(getGenericEventMapping(event, undefined)).toBeNull();
    });

    it("should handle mapping without parameters", () => {
      const simpleMapping: GenericEventMapping[] = [
        {
          sourceEvent: "simple_event",
          targetEvent: "Simple Event",
          // No parameters
        },
      ];

      const event: AnalyticsClientEvent = {
        type: "track",
        event: "simple_event",
        messageId: "msg-123",
        anonymousId: "anon-123",
        context: {},
      };

      const result = getGenericEventMapping(event, simpleMapping);
      expect(result).toEqual({
        targetEvent: "Simple Event",
        parameters: {},
        itemMappings: undefined,
      });
    });

    it("should handle mapping without targetEvent", () => {
      const mappingWithoutTarget: GenericEventMapping[] = [
        {
          sourceEvent: "test_event",
          targetEvent: "", // Empty target event
          parameters: [{ sourceProperty: "properties.value", targetProperty: "value" }],
        },
      ];

      const event: AnalyticsClientEvent = {
        type: "track",
        event: "test_event",
        messageId: "msg-123",
        anonymousId: "anon-123",
        properties: {
          value: "test",
        },
        context: {},
      };

      const result = getGenericEventMapping(event, mappingWithoutTarget);
      expect(result).toEqual({
        targetEvent: undefined,
        parameters: {
          value: "test",
        },
        itemMappings: undefined,
      });
    });

    it("should handle deeply nested array access", () => {
      const deepMapping: GenericEventMapping[] = [
        {
          sourceEvent: "complex_event",
          targetEvent: "Complex Event",
          parameters: [
            {
              sourceProperty: "properties.data.items[*].details.name",
              targetProperty: "items",
            },
          ],
        },
      ];

      const event: AnalyticsClientEvent = {
        type: "track",
        event: "complex_event",
        messageId: "msg-123",
        anonymousId: "anon-123",
        properties: {
          data: {
            items: [{ details: { name: "Item 1", price: 10 } }, { details: { name: "Item 2", price: 20 } }],
          },
        },
        context: {},
      };

      const result = getGenericEventMapping(event, deepMapping);
      expect(result?.itemMappings).toEqual([
        {
          key: "properties.data.items[*].details.name",
          value: "items",
          arrayValue: [{ details: { name: "Item 1", price: 10 } }, { details: { name: "Item 2", price: 20 } }],
          remainingPath: "details.name",
        },
      ]);
    });

    it("should handle multiple array mappings", () => {
      const multiArrayMapping: GenericEventMapping[] = [
        {
          sourceEvent: "multi_array_event",
          targetEvent: "Multi Array Event",
          parameters: [
            { sourceProperty: "properties.products[*].id", targetProperty: "product_ids" },
            { sourceProperty: "properties.products[*].name", targetProperty: "product_names" },
            { sourceProperty: "properties.categories[*].title", targetProperty: "category_titles" },
          ],
        },
      ];

      const event: AnalyticsClientEvent = {
        type: "track",
        event: "multi_array_event",
        messageId: "msg-123",
        anonymousId: "anon-123",
        properties: {
          products: [
            { id: 1, name: "Product 1" },
            { id: 2, name: "Product 2" },
          ],
          categories: [{ title: "Category 1" }, { title: "Category 2" }],
        },
        context: {},
      };

      const result = getGenericEventMapping(event, multiArrayMapping);
      expect(result?.itemMappings).toHaveLength(3);
      expect(result?.itemMappings?.[0]).toMatchObject({
        key: "properties.products[*].id",
        value: "product_ids",
        remainingPath: "id",
      });
      expect(result?.itemMappings?.[1]).toMatchObject({
        key: "properties.products[*].name",
        value: "product_names",
        remainingPath: "name",
      });
      expect(result?.itemMappings?.[2]).toMatchObject({
        key: "properties.categories[*].title",
        value: "category_titles",
        remainingPath: "title",
      });
    });

    it("should handle event without type or event name", () => {
      const malformedEvent = {
        messageId: "msg-123",
        anonymousId: "anon-123",
        context: {},
      } as AnalyticsClientEvent;

      const result = getGenericEventMapping(malformedEvent, mappings);
      expect(result).toBeNull();
    });

    it("should handle mapping with invalid source event type", () => {
      const invalidMapping = [
        {
          sourceEvent: null, // Invalid source event
          targetEvent: "Target",
          parameters: [],
        },
        {
          // Missing sourceEvent
          targetEvent: "Target",
          parameters: [],
        },
      ] as any;

      const event: AnalyticsClientEvent = {
        type: "track",
        event: "test",
        messageId: "msg-123",
        anonymousId: "anon-123",
        context: {},
      };

      const result = getGenericEventMapping(event, invalidMapping);
      expect(result).toBeNull();
    });

    it("should handle complex nested properties with special characters", () => {
      const specialMapping: GenericEventMapping[] = [
        {
          sourceEvent: "special_event",
          targetEvent: "Special Event",
          parameters: [
            { sourceProperty: "properties.data['special-key']", targetProperty: "special_value" },
            { sourceProperty: "properties.nested.deep.value", targetProperty: "deep_value" },
          ],
        },
      ];

      const event: AnalyticsClientEvent = {
        type: "track",
        event: "special_event",
        messageId: "msg-123",
        anonymousId: "anon-123",
        properties: {
          data: {
            "special-key": "special value",
          },
          nested: {
            deep: {
              value: "deep nested value",
            },
          },
        },
        context: {},
      };

      const result = getGenericEventMapping(event, specialMapping);
      // Note: The current implementation might not handle bracket notation,
      // so we expect it to not find the special-key value
      expect(result).toEqual({
        targetEvent: "Special Event",
        parameters: {
          deep_value: "deep nested value",
        },
        itemMappings: undefined,
      });
    });

    it("should handle events with undefined properties object", () => {
      const event: AnalyticsClientEvent = {
        type: "track",
        event: "page_viewed",
        messageId: "msg-123",
        anonymousId: "anon-123",
        // properties is undefined
        context: {},
      } as any;

      const result = getGenericEventMapping(event, mappings);
      expect(result).toEqual({
        targetEvent: "Page Viewed",
        parameters: {},
        itemMappings: undefined,
      });
    });

    it("should handle circular references gracefully", () => {
      const circular: any = { a: 1 };
      circular.self = circular;

      const event: AnalyticsClientEvent = {
        type: "track",
        event: "circular_event",
        messageId: "msg-123",
        anonymousId: "anon-123",
        properties: circular,
        context: {},
      };

      const circularMapping: GenericEventMapping[] = [
        {
          sourceEvent: "circular_event",
          targetEvent: "Circular Event",
          parameters: [{ sourceProperty: "properties.a", targetProperty: "a_value" }],
        },
      ];

      const result = getGenericEventMapping(event, circularMapping);
      expect(result).toEqual({
        targetEvent: "Circular Event",
        parameters: {
          a_value: 1,
        },
        itemMappings: undefined,
      });
    });
  });

  describe("getGenericEventMappings", () => {
    const mappings: GenericEventMapping[] = [
      {
        sourceEvent: "page_viewed",
        targetEvent: "Page Viewed",
        parameters: [
          { sourceProperty: "properties.url", targetProperty: "page_url" },
          { sourceProperty: "properties.title", targetProperty: "page_title" },
        ],
      },
      {
        sourceEvent: "product_added",
        targetEvent: "Add to Cart",
        parameters: [
          { sourceProperty: "properties.product_id", targetProperty: "product.id" },
          { sourceProperty: "properties.items[*].name", targetProperty: "product.items" },
        ],
      },
    ];

    const multipleMappings: GenericEventMapping[] = [
      {
        sourceEvent: "purchase",
        targetEvent: "GA4 Purchase",
        parameters: [
          { sourceProperty: "properties.revenue", targetProperty: "value" },
          { sourceProperty: "properties.currency", targetProperty: "currency" },
        ],
      },
      {
        sourceEvent: "purchase",
        targetEvent: "Facebook Purchase",
        parameters: [
          { sourceProperty: "properties.revenue", targetProperty: "purchase_value" },
          { sourceProperty: "properties.order_id", targetProperty: "order_id" },
        ],
      },
      {
        sourceEvent: "purchase",
        targetEvent: "Custom Purchase",
        parameters: [{ sourceProperty: "properties.total", targetProperty: "amount" }],
      },
    ];

    it("should return array with single mapping result", () => {
      const event: AnalyticsClientEvent = {
        type: "track",
        event: "page_viewed",
        messageId: "msg-123",
        anonymousId: "anon-123",
        properties: {
          url: "https://example.com",
          title: "Example Page",
        },
        context: {},
      };

      const result = getGenericEventMappings(event, mappings);
      expect(result).toHaveLength(1);
      expect(result?.[0]).toEqual({
        targetEvent: "Page Viewed",
        parameters: {
          page_url: "https://example.com",
          page_title: "Example Page",
        },
        itemMappings: undefined,
      });
    });

    it("should return array with multiple mapping results for same source event", () => {
      const event: AnalyticsClientEvent = {
        type: "track",
        event: "purchase",
        messageId: "msg-123",
        anonymousId: "anon-123",
        properties: {
          revenue: 100,
          currency: "USD",
          order_id: "order-123",
          total: 100,
        },
        context: {},
      };

      const result = getGenericEventMappings(event, multipleMappings);
      expect(result).toHaveLength(3);

      expect(result?.[0]).toEqual({
        targetEvent: "GA4 Purchase",
        parameters: {
          value: 100,
          currency: "USD",
        },
        itemMappings: undefined,
      });

      expect(result?.[1]).toEqual({
        targetEvent: "Facebook Purchase",
        parameters: {
          purchase_value: 100,
          order_id: "order-123",
        },
        itemMappings: undefined,
      });

      expect(result?.[2]).toEqual({
        targetEvent: "Custom Purchase",
        parameters: {
          amount: 100,
        },
        itemMappings: undefined,
      });
    });

    it("should handle array access in multiple mappings", () => {
      const arrayMappings: GenericEventMapping[] = [
        {
          sourceEvent: "multi_product",
          targetEvent: "GA4 Multi Product",
          parameters: [{ sourceProperty: "properties.items[*].id", targetProperty: "product_ids" }],
        },
        {
          sourceEvent: "multi_product",
          targetEvent: "Facebook Multi Product",
          parameters: [{ sourceProperty: "properties.items[*].name", targetProperty: "product_names" }],
        },
      ];

      const event: AnalyticsClientEvent = {
        type: "track",
        event: "multi_product",
        messageId: "msg-123",
        anonymousId: "anon-123",
        properties: {
          items: [
            { id: "1", name: "Product 1" },
            { id: "2", name: "Product 2" },
          ],
        },
        context: {},
      };

      const result = getGenericEventMappings(event, arrayMappings);
      expect(result).toHaveLength(2);

      expect(result?.[0]).toEqual({
        targetEvent: "GA4 Multi Product",
        parameters: {},
        itemMappings: [
          {
            key: "properties.items[*].id",
            value: "product_ids",
            arrayValue: [
              { id: "1", name: "Product 1" },
              { id: "2", name: "Product 2" },
            ],
            remainingPath: "id",
          },
        ],
      });

      expect(result?.[1]).toEqual({
        targetEvent: "Facebook Multi Product",
        parameters: {},
        itemMappings: [
          {
            key: "properties.items[*].name",
            value: "product_names",
            arrayValue: [
              { id: "1", name: "Product 1" },
              { id: "2", name: "Product 2" },
            ],
            remainingPath: "name",
          },
        ],
      });
    });

    it("should return null for unmapped events", () => {
      const event: AnalyticsClientEvent = {
        type: "track",
        event: "unmapped_event",
        messageId: "msg-123",
        anonymousId: "anon-123",
        context: {},
      };

      const result = getGenericEventMappings(event, mappings);
      expect(result).toBeNull();
    });

    it("should return null for empty mappings", () => {
      const event: AnalyticsClientEvent = {
        type: "track",
        event: "any_event",
        messageId: "msg-123",
        anonymousId: "anon-123",
        context: {},
      };

      expect(getGenericEventMappings(event, [])).toBeNull();
      expect(getGenericEventMappings(event, undefined)).toBeNull();
    });

    it("should handle mappings with mixed valid and invalid parameters", () => {
      const mixedMappings: GenericEventMapping[] = [
        {
          sourceEvent: "test_event",
          targetEvent: "First Event",
          parameters: [
            { sourceProperty: "properties.valid1", targetProperty: "valid1" },
            // @ts-ignore - Testing invalid parameter
            { sourceProperty: "invalid" }, // Missing targetProperty
          ] as any,
        },
        {
          sourceEvent: "test_event",
          targetEvent: "Second Event",
          parameters: [
            { sourceProperty: "properties.valid2", targetProperty: "valid2" },
            // @ts-ignore - Testing invalid parameter
            null, // Null parameter
          ] as any,
        },
      ];

      const event: AnalyticsClientEvent = {
        type: "track",
        event: "test_event",
        messageId: "msg-123",
        anonymousId: "anon-123",
        properties: {
          valid1: "value1",
          valid2: "value2",
        },
        context: {},
      };

      const result = getGenericEventMappings(event, mixedMappings);
      expect(result).toHaveLength(2);
      expect(result?.[0]).toEqual({
        targetEvent: "First Event",
        parameters: {
          valid1: "value1",
        },
        itemMappings: undefined,
      });
      expect(result?.[1]).toEqual({
        targetEvent: "Second Event",
        parameters: {
          valid2: "value2",
        },
        itemMappings: undefined,
      });
    });

    it("should handle non-track events by type with multiple mappings", () => {
      const event: AnalyticsServerEvent = {
        type: "identify",
        messageId: "msg-123",
        anonymousId: "anon-123",
        traits: {
          email: "<EMAIL>",
          name: "Test User",
        },
        context: {},
      };

      const identifyMappings: GenericEventMapping[] = [
        {
          sourceEvent: "identify",
          targetEvent: "GA4 User Identified",
          parameters: [{ sourceProperty: "traits.email", targetProperty: "email" }],
        },
        {
          sourceEvent: "identify",
          targetEvent: "CRM User Identified",
          parameters: [
            { sourceProperty: "traits.email", targetProperty: "user_email" },
            { sourceProperty: "traits.name", targetProperty: "user_name" },
          ],
        },
      ];

      const result = getGenericEventMappings(event, identifyMappings, { matchOnEventName: false });
      expect(result).toHaveLength(2);
      expect(result?.[0].targetEvent).toBe("GA4 User Identified");
      expect(result?.[1].targetEvent).toBe("CRM User Identified");
    });

    it("should handle custom event key with multiple mappings", () => {
      const event: AnalyticsClientEvent = {
        type: "track",
        event: "ignored",
        messageId: "msg-123",
        anonymousId: "anon-123",
        properties: {
          customEventName: "purchase",
        },
        context: {},
      };

      const result = getGenericEventMappings(event, multipleMappings, {
        eventKey: "properties.customEventName",
      });
      expect(result).toHaveLength(3);
      expect(result?.[0].targetEvent).toBe("GA4 Purchase");
      expect(result?.[1].targetEvent).toBe("Facebook Purchase");
      expect(result?.[2].targetEvent).toBe("Custom Purchase");
    });

    it("should maintain backward compatibility with getGenericEventMapping", () => {
      const event: AnalyticsClientEvent = {
        type: "track",
        event: "purchase",
        messageId: "msg-123",
        anonymousId: "anon-123",
        properties: {
          revenue: 100,
          currency: "USD",
        },
        context: {},
      };

      // getGenericEventMappings should return all mappings
      const multipleResults = getGenericEventMappings(event, multipleMappings);
      expect(multipleResults).toHaveLength(3);

      // getGenericEventMapping should return only the first mapping
      const singleResult = getGenericEventMapping(event, multipleMappings);
      expect(singleResult).toEqual(multipleResults?.[0]);
    });
  });
});
