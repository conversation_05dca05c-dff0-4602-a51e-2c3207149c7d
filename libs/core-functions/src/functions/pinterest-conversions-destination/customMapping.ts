import { randomUUID } from "crypto";
import { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { extractUserData } from "@jitsu/event-extractors";
import type { JSONObject } from "@jitsu/protocols/analytics";

interface Product {
  id: string;
  item_price?: string;
  quantity?: number;
  item_name?: string;
  item_category?: string;
  item_brand?: string;
}

export const customMapping = (event: AnalyticsServerEvent, appName: string): JSONObject => {
  const { context = {}, properties = {}, messageId, userId, anonymousId } = event;
  const { traits = {}, page, device, network, app, locale } = context;
  const address = context?.traits?.address || traits?.address || properties?.address || ({} as any);
  const email = (properties?.email || context?.traits?.email || traits?.email) as string;
  const phone = (properties?.phone || context?.traits?.phone || traits?.phone) as string;
  const firstName = (properties?.first_name || context?.traits?.firstName || traits?.firstName) as string;
  const lastName = (properties?.last_name || context?.traits?.lastName || traits?.lastName) as string;

  const { ip, userAgent } = extractUserData(event);

  let pinterestClickId = "";
  if (page?.url) {
    try {
      pinterestClickId = new URL(page.url).searchParams.get("epik") || "";
    } catch {
      // Invalid URL - ignore
    }
  }

  let dateOfBirth;
  if (traits.birthday) {
    const date = new Date(String(traits.birthday));
    if (!isNaN(date.getTime())) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      dateOfBirth = [`${year}${month}${day}`];
    }
  }

  const userData = {
    email: email ? [email] : undefined,
    external_id: userId ? [userId] : anonymousId ? [anonymousId] : undefined,
    phone: phone ? [phone] : undefined,
    first_name: firstName ? [firstName] : undefined,
    last_name: lastName ? [lastName] : undefined,
    gender: traits.gender ? [traits.gender] : undefined,
    date_of_birth: dateOfBirth,
    city: address?.city ? [address.city] : undefined,
    state: address?.state ? [address.state] : undefined,
    zip: address?.postalCode ? [address.postalCode] : undefined,
    country: address.country ? [address.country] : undefined,
    client_ip_address: ip,
    client_user_agent: userAgent || "Mozilla/5.0",
    hashed_maids: [device?.advertisingId, device?.idfa, device?.adid].filter(Boolean),
    click_id: pinterestClickId,
    partner_id: properties.partner_id || context.campaign?.name,
  };

  const extractProduct = (product: any): Product | null => {
    // Handle different product ID formats
    const productId = product.product_id || product.id || product.sku || product.variant;

    if (!productId) {
      return null;
    }

    return {
      id: String(productId),
      item_price: product.price !== undefined ? String(product.price) : undefined,
      quantity: product.quantity !== undefined ? Number(product.quantity) : 1,
      item_name: product.name || product.product_name,
      item_category: product.category || product.product_category,
      item_brand: product.brand,
    };
  };

  let products: Product[] = [];
  let productIds: string[] = [];

  if (Array.isArray(properties.products) && properties.products.length > 0) {
    // Extract products from the products array
    const extractedProducts = properties.products.map(extractProduct).filter(p => p !== null);

    products = extractedProducts;
    productIds = extractedProducts.map(p => p.id);
  } else if (properties.product_id || properties.id || properties.sku) {
    // Handle single product case
    const productId = properties.product_id || properties.id || properties.sku;
    if (productId) {
      productIds = [String(productId)];

      const singleProduct = extractProduct(properties);
      if (singleProduct) {
        products = [singleProduct];
      }
    }
  }

  // Pinterest reccomends fillng this field in even for page visit events so we are using the page title
  const contentIds = productIds.length > 0 ? productIds : properties?.title || context?.page?.title;

  let value = properties.value || properties.price || properties.revenue;
  if (value === undefined && products.length > 0) {
    value = products.reduce((sum, item) => {
      const price = item.item_price ? parseFloat(item.item_price) : 0;
      const quantity = item.quantity || 1;
      return sum + price * quantity;
    }, 0);
  }
  value = value && String(value);

  let totalItems = properties.quantity;
  if (totalItems === undefined && Array.isArray(properties.products)) {
    totalItems = properties.products.reduce((sum: number, p: any) => sum + (Number(p?.quantity) || 1), 0);
  }

  const currency = properties.currency || (properties as any).meta?.i18n?.currency || "USD";

  return {
    event_id: messageId || randomUUID(),
    partner_name: "chord",
    event_source_url: page?.url,
    opt_out: context?.opt_out,
    user_data: userData,
    custom_data: {
      currency: currency,
      value: value ?? null,
      content_ids: contentIds,
      contents: products.length > 0 ? (products as any) : null,
      num_items: totalItems !== undefined ? Number(totalItems) : null,
      order_id: properties.order_id || properties.cart_id,
      search_string: properties.query || properties.search_term,
      opt_out_type: properties.opt_out_type || context.opt_out_type,
    },
    app_name: appName,
    app_id: app?.id,
    app_version: app?.version,
    device_brand: device?.brand,
    device_model: device?.model,
    device_type: device?.type,
    device_carrier: network?.carrier,
    os_version: context.os?.version,
    language: locale?.split("-")[0],
    ...(network?.wifi !== undefined ? { wifi: network.wifi } : {}),
  };
};
