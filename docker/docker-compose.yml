version: "3.8"
services:
  kafka:
    tty: true
    image: "bitnami/kafka:3.6.0"
    environment:
      TERM: "xterm-256color"
      KAFKA_CFG_NODE_ID: 0
      KAFKA_CFG_PROCESS_ROLES: controller,broker
      KAFKA_CFG_LISTENERS: PLAINTEXT://:9092,CONTROLLER://:9093
      KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP: CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT
      KAFKA_CFG_CONTROLLER_QUORUM_VOTERS: 0@kafka:9093
      KAFKA_CFG_CONTROLLER_LISTENER_NAMES: CONTROLLER
    healthcheck:
      test: ["CMD-SHELL", "kafka-topics.sh --bootstrap-server 127.0.0.1:9092 --list"]
      interval: 5s
      timeout: 10s
      retries: 30
  #    volumes:
  #      - ./data/kafka:/bitnami/kafka

  #
  #  redis:
  #    tty: true
  #    image: redis:6.2-alpine
  #    restart: "unless-stopped"
  #    command: "redis-server --save 20 1 --loglevel warning --requirepass ${REDIS_PASSWORD:-default}"
  #  #    volumes:
  #  #      - ./data/redis:/var/lib/redis

  clickhouse:
    tty: true
    image: clickhouse/clickhouse-server:24.6
    restart: "unless-stopped"
    environment:
      - CLICKHOUSE_DB=newjitsu_metrics
      - CLICKHOUSE_USER=default
      - CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT=1
      - CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD:-default}
    logging:
      options:
        max-size: 10m
        max-file: "3"

  mongo:
    image: mongo
    restart: "unless-stopped"
    environment:
      MONGO_INITDB_ROOT_USERNAME: default
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD:-default}

  postgres:
    tty: true
    image: postgres:14
    restart: "unless-stopped"
    user: postgres
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-default}
    logging:
      options:
        max-size: 10m
        max-file: "3"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready", "-d", "postgres"]
      interval: 1s
      timeout: 10s
      retries: 10
    ports:
      - "${EXTERNAL_POSTGRES_PORT:-5432}:5432"
  ## permissions problem is not that trivial to solve https://forums.docker.com/t/data-directory-var-lib-postgresql-data-pgdata-has-wrong-ownership/17963/42
  #    volumes:
  #      - ./data/postgres:/var/lib/postgresql/data

  console:
    tty: true
    image: jitsucom/console:${DOCKER_TAG:-latest}
    restart: "unless-stopped"
    platform: linux/amd64
    environment:
      ROTOR_URL: "http://rotor:3401"
      ROTOR_AUTH_KEY: ${BULKER_TOKEN:-default}
      BULKER_URL: "http://bulker:3042"
      CONSOLE_RAW_AUTH_TOKENS: ${CONSOLE_TOKEN:-default}
      BULKER_AUTH_KEY: ${BULKER_TOKEN:-default}
      MIT_COMPLIANT: ${MIT_COMPLIANT:-false}
      DATABASE_URL: "postgresql://postgres:${POSTGRES_PASSWORD:-default}@postgres:5432/postgres?schema=newjitsu"
      SEED_USER_EMAIL: ${SEED_USER_EMAIL:-}
      SEED_USER_PASSWORD: ${SEED_USER_PASSWORD:-}
      ENABLE_CREDENTIALS_LOGIN: ${ENABLE_CREDENTIALS_LOGIN:-true}
      GITHUB_CLIENT_ID: ${GITHUB_CLIENT_ID}
      GITHUB_CLIENT_SECRET: ${GITHUB_CLIENT_SECRET}
      SYNCS_ENABLED: ${SYNCS_ENABLED:-false}
      SYNCCTL_URL: "http://syncctl:${EXTERNAL_SYNCS_PORT:-3043}"
      SYNCCTL_AUTH_KEY: ${SYNCCTL_TOKEN:-default}
      GOOGLE_SCHEDULER_KEY: ${GOOGLE_SCHEDULER_KEY}
      JITSU_INGEST_PUBLIC_URL: "${JITSU_INGEST_PUBLIC_URL:-http://localhost:${JITSU_INGEST_PORT:-8080}/}"
      JITSU_PUBLIC_URL: "${JITSU_PUBLIC_URL:-${NEXTAUTH_URL:-http://localhost:${JITSU_UI_PORT:-3000}/}}"
      NEXTAUTH_URL: "${JITSU_PUBLIC_URL:-${NEXTAUTH_URL:-http://localhost:${JITSU_UI_PORT:-3000}/}}"
      CLICKHOUSE_HOST: "clickhouse:8123"
      CLICKHOUSE_PASSWORD: "${CLICKHOUSE_PASSWORD:-default}"
      CLICKHOUSE_DATABASE: "newjitsu_metrics"
      MONGODB_URL: "mongodb://default:${MONGO_PASSWORD:-default}@mongo/"
      MONGODB_NETWORK_COMPRESSION: "none"
      FORCE_UPDATE_DB: "true"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://console:3000/api/healthcheck"]
      interval: 2s
      timeout: 10s
      retries: 30
    extra_hosts:
      - "syncctl:host-gateway"
    depends_on:
      clickhouse:
        condition: service_started
      postgres:
        condition: service_healthy
    ports:
      - "${JITSU_UI_PORT:-3000}:3000"

  sync-catalog-init:
    tty: true
    image: curlimages/curl
    restart: "on-failure"
    environment:
      CONSOLE_TOKEN: ${CONSOLE_TOKEN:-default}
    command: "curl --silent --output nul --show-error -H 'Authorization: Bearer service-admin-account:${CONSOLE_TOKEN:-default}' http://console:3000/api/admin/catalog-refresh?initial=true"
    depends_on:
      console:
        condition: service_healthy

  bulker:
    tty: true
    image: jitsucom/bulker:${DOCKER_TAG:-latest}
    platform: linux/amd64
    restart: "unless-stopped"
    environment:
      TERM: "xterm-256color"
      BULKER_KAFKA_BOOTSTRAP_SERVERS: "kafka:9092"
      BULKER_RAW_AUTH_TOKENS: ${BULKER_TOKEN:-default}
      BULKER_CONFIG_SOURCE: "http://console:3000/api/admin/export/bulker-connections"
      BULKER_CONFIG_SOURCE_HTTP_AUTH_TOKEN: "service-admin-account:${CONSOLE_TOKEN:-default}"
      BULKER_CACHE_DIR: "/tmp/cache"
      BULKER_INTERNAL_TASK_LOG: '{"id":"task_log","metricsKeyPrefix":"syncs","usesBulker":true,"type":"postgres","options":{"mode":"stream"},"credentials":{"host":"postgres","port":5432,"sslMode":"disable","database":"postgres","password":"${POSTGRES_PASSWORD:-default}","username":"postgres","defaultSchema":"newjitsu"}}'
      BULKER_CLICKHOUSE_HOST: "clickhouse:8123"
      BULKER_CLICKHOUSE_PASSWORD: "${CLICKHOUSE_PASSWORD:-default}"
      BULKER_CLICKHOUSE_DATABASE: "newjitsu_metrics"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://bulker:3042/health"]
      interval: 2s
      timeout: 10s
      retries: 15
    depends_on:
      console:
        condition: service_healthy
      kafka:
        condition: service_healthy

  rotor:
    tty: true
    image: jitsucom/rotor:${DOCKER_TAG:-latest}
    platform: linux/amd64
    restart: "unless-stopped"
    environment:
      ROTOR_RAW_AUTH_TOKENS: ${BULKER_TOKEN:-default}
      BULKER_URL: "http://bulker:3042"
      BULKER_AUTH_KEY: ${BULKER_TOKEN:-default}
      KAFKA_BOOTSTRAP_SERVERS: "kafka:9092"
      #      REDIS_URL: "redis://default:${REDIS_PASSWORD:-default}@redis:6379"
      REPOSITORY_BASE_URL: "http://console:3000/api/admin/export/"
      REPOSITORY_AUTH_TOKEN: "service-admin-account:${CONSOLE_TOKEN:-default}"
      REPOSITORY_CACHE_DIR: "/tmp/cache"
      MONGODB_URL: "mongodb://default:${MONGO_PASSWORD:-default}@mongo/"
      CLICKHOUSE_HOST: "clickhouse:8123"
      CLICKHOUSE_PASSWORD: "${CLICKHOUSE_PASSWORD:-default}"
      CLICKHOUSE_DATABASE: "newjitsu_metrics"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://rotor:3401/health"]
      interval: 5s
      timeout: 10s
      retries: 15
    depends_on:
      console:
        condition: service_healthy
      bulker:
        condition: service_healthy

  ingest:
    tty: true
    image: jitsucom/ingest:${DOCKER_TAG:-latest}
    platform: linux/amd64
    restart: "unless-stopped"
    environment:
      TERM: "xterm-256color"
      INGEST_PUBLIC_URL: "${JITSU_INGEST_PUBLIC_URL:-http://localhost:${JITSU_INGEST_PORT:-8080}/}"
      INGEST_KAFKA_BOOTSTRAP_SERVERS: "kafka:9092"
      INGEST_RAW_AUTH_TOKENS: ${BULKER_TOKEN:-default}
      INGEST_REPOSITORY_URL: "http://console:3000/api/admin/export/streams-with-destinations"
      INGEST_SCRIPT_ORIGIN: "http://console:3000/api/s/javascript-library"
      INGEST_REPOSITORY_AUTH_TOKEN: "service-admin-account:${CONSOLE_TOKEN:-default}"
      INGEST_CACHE_DIR: "/tmp/cache"
      INGEST_ROTOR_URL: "http://rotor:3401"
      INGEST_ROTOR_AUTH_KEY: ${BULKER_TOKEN:-default}
      INGEST_CLICKHOUSE_HOST: "clickhouse:8123"
      INGEST_CLICKHOUSE_PASSWORD: "${CLICKHOUSE_PASSWORD:-default}"
      INGEST_CLICKHOUSE_DATABASE: "newjitsu_metrics"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://ingest:3049/health"]
      interval: 2s
      timeout: 10s
      retries: 15
    depends_on:
      console:
        condition: service_healthy
      rotor:
        condition: service_started
    ports:
      - "${JITSU_INGEST_PORT:-8080}:3049"

  syncctl:
    tty: true
    image: jitsucom/syncctl:${DOCKER_TAG:-latest}
    platform: linux/amd64
    restart: "on-failure"
    environment:
      TERM: "xterm-256color"
      HTTP_PORT: ${EXTERNAL_SYNCS_PORT:-3043}
      SYNCCTL_SYNCS_ENABLED: ${SYNCS_ENABLED:-false}
      SYNCCTL_RAW_AUTH_TOKENS: ${SYNCCTL_TOKEN:-default}
      SYNCCTL_DATABASE_URL: "postgresql://postgres:${POSTGRES_PASSWORD:-default}@127.0.0.1:${EXTERNAL_POSTGRES_PORT:-5432}/postgres?search_path=newjitsu"
      SYNCCTL_SIDECAR_DATABASE_URL: "postgresql://postgres:${POSTGRES_PASSWORD:-default}@${EXTERNAL_POSTGRES_HOST}:${EXTERNAL_POSTGRES_PORT:-5432}/postgres?search_path=newjitsu"
      SYNCCTL_KUBERNETES_CLIENT_CONFIG: "${SYNCCTL_KUBERNETES_CLIENT_CONFIG:-local}"
      SYNCCTL_KUBERNETES_CONTEXT: "${SYNCCTL_KUBERNETES_CONTEXT}"
    network_mode: "host"
    depends_on:
      bulker:
        condition: service_healthy
