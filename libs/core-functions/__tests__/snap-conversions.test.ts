import { testJitsuFunction, TestOptions } from "./lib/testing-lib";
import SnapConversionsDestination from "../src/functions/snap-conversions-destination";
import { SnapConversionsCredentials } from "../src/meta";
import { eventsSequence } from "./lib/test-data-chord";

// npx pnpm tsc -p . && PIXEL_ID="************************************" ACCESS_TOKEN="" npx pnpm jest --verbose -t 'snap-conversions-destination-integration'
test("snap-conversions-destination-integration", async () => {
  if (!process.env.PIXEL_ID && !process.env.ACCESS_TOKEN) {
    console.log("Skipping Snap destination integration test - PIXEL_ID or accessToken is not set");
    return;
  }

  const opts: TestOptions<SnapConversionsCredentials> = {
    func: SnapConversionsDestination,
    config: {
      pixelId: process.env.PIXEL_ID || "",
      accessToken: process.env.ACCESS_TOKEN || "",
      testMode: true,
    },
    events: eventsSequence(),
  };
  await testJitsuFunction(opts);
});
