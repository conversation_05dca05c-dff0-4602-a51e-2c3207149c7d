import type { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { FullContext } from "@jitsu/protocols/functions";
import type { NinetailedCloudCredentials } from "../../../meta";
import { formatEventForNinetailed } from "../formatter";

/**
 * Sends tracking event data to Ninetailed API
 * @param event Analytics event to be processed
 * @param ctx Function execution context
 * @returns API response from Ninetailed
 */
export const reportEvents = async (event: AnalyticsServerEvent, ctx: FullContext) => {
  try {
    const props = ctx.props as NinetailedCloudCredentials;
    const apiUrl = `https://experience.ninetailed.co/v2/organizations/${props.organizationId}/environments/${props.environmentSlug}/events`;

    const formattedEvent = formatEventForNinetailed(event);

    const requestBody = {
      events: [formattedEvent],
    };

    const response = await ctx.fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.text();
      const errorMsg = `Ninetailed API error: ${response.status} ${response.statusText} - ${errorData}`;
      ctx.log.error(errorMsg, {
        statusCode: response.status,
        rawEventData: event,
        sentData: requestBody,
        responseData: errorData,
      });
      throw new Error(errorMsg);
    }

    const responseData = await response.json();

    ctx.log.debug("Ninetailed event tracking successful", {
      eventId: event.messageId,
      responseStatus: response.status,
      rawEventData: event,
      sentData: requestBody,
      responseData,
    });

    return responseData;
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : String(err);
    ctx.log.error("Ninetailed event tracking failed", { error: errorMessage, event });
    throw err;
  }
};
