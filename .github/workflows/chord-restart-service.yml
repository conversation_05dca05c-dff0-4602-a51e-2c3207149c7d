name: Restart Services
run-name: Restart ${{ inputs.service }} in ${{ inputs.environment }}

on:
  workflow_dispatch:
    inputs:
      service:
        description: "Service to restart"
        required: true
        type: choice
        options:
          - "console"
          - "rotor"
        default: "rotor"
      environment:
        description: "Environment to restart"
        required: true
        type: choice
        options:
          - "development"
          - "staging"
          - "production"
        default: "development"

permissions:
  id-token: write
  contents: read

jobs:
  restart_development:
    if: ${{ inputs.environment == 'development' }}
    name: Restart development services
    runs-on: ubuntu-latest
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::501922849989:role/ECRBuildAndPushRole
          aws-region: us-east-1
          role-session-name: Github_to_AWS_via_FederatedOIDC

      - name: Restart development console
        if: ${{ inputs.service == 'console' }}
        run: |
          aws ecs update-service --cluster cdp-development --service console --force-new-deployment

      - name: Restart development rotor
        if: ${{ inputs.service == 'rotor' }}
        run: |
          aws ecs update-service --cluster cdp-development --service rotor --force-new-deployment

  restart_staging:
    if: ${{ inputs.environment == 'staging' }}
    name: Restart staging services
    runs-on: ubuntu-latest
    steps:
      - name: Configure AWS credentials for staging
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::470689186102:role/ECRBuildAndPushRole
          aws-region: us-east-1
          role-session-name: Github_to_AWS_via_FederatedOIDC

      - name: Restart staging console
        if: ${{ inputs.service == 'console' }}
        run: |
          aws ecs update-service --cluster cdp-staging --service console --force-new-deployment

      - name: Restart staging rotor
        if: ${{ inputs.service == 'rotor' }}
        run: |
          aws ecs update-service --cluster cdp-staging --service rotor --force-new-deployment

  restart_production:
    if: ${{ inputs.environment == 'production' }}
    name: Restart production services
    runs-on: ubuntu-latest
    steps:
      - name: Configure AWS credentials for production
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::495030305857:role/ECRBuildAndPushRole
          aws-region: us-east-1
          role-session-name: Github_to_AWS_via_FederatedOIDC

      - name: Restart production console
        if: ${{ inputs.service == 'console' }}
        run: |
          aws ecs update-service --cluster cdp-production --service console --force-new-deployment

      - name: Restart production rotor
        if: ${{ inputs.service == 'rotor' }}
        run: |
          aws ecs update-service --cluster cdp-production --service rotor --force-new-deployment
