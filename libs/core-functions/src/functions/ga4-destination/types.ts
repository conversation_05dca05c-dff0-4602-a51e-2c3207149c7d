export type Ga4Request = {
  app_instance_id?: string;
  client_id?: string;
  user_id?: string;
  timestamp_micros: number;
  user_properties?: Record<string, any>;
  events: Ga4Event[];
};

export type Ga4Event = {
  name: string;
  params?: Record<string, any>;
};

export type Ga4Item = {
  item_id: string;
  item_name: string;
  affiliation?: string;
  coupon?: string;
  creative_name?: string;
  creative_slot?: string;
  currency?: string;
  discount?: number;
  index?: number;
  item_brand?: string;
  item_category?: string;
  item_category2?: string;
  item_category3?: string;
  item_category4?: string;
  item_category5?: string;
  item_list_id?: string;
  item_list_name?: string;
  item_variant?: string;
  location_id?: string;
  price?: number;
  promotion_id?: string;
  promotion_name?: string;
  quantity?: number;
};
