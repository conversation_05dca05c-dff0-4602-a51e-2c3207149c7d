import { AnalyticsClientEvent, AnalyticsContext } from "@jitsu/protocols/analytics";
import { applyFilters, CommonDestinationCredentials, InternalPlugin } from "./index";

/**
 * Impact Destination Credentials
 * Contains all configuration options needed to connect to Impact's tracking APIs
 */
export type ImpactDestinationCredentials = {
  campaignId?: string; // Impact campaign identifier
  accountSid?: string; // Account SID for authentication
  apiKey?: string; // API key for authentication
  iOsAppId?: string; // iOS app identifier
  androidAppId?: string; // Android app identifier
  eventTypeId?: string; // Custom event type ID
  enablePageEvents?: boolean; // Whether to track page view events
  enableScreenEvents?: boolean; // Whether to track screen view events
  enableIdentifyEvents?: boolean; // Whether to track identify events
  customParameterMapping?: Record<string, string>; // Custom field mappings
  customMappingForProducts?: Record<string, string>; // Custom product field mappings
  installEventNames?: string[]; // Custom event names for app installs
  actionEventNames?: string[]; // Custom event names for conversions
  pageLoadEventNames?: string[]; // Custom event names for page views
  screenLoadEventNames?: string[]; // Custom event names for screen views
} & CommonDestinationCredentials;

import { extractEventData, extractOrderData as extractOrder, extractProducts } from "@jitsu/event-extractors";

/**
 * API Endpoints for Impact tracking
 */
const ENDPOINTS = {
  PAGE_LOAD: "https://trkapi.impact.com/PageLoad",
  CONVERSION_PREFIX: "https://api.impact.com/Advertisers",
  APPLICATION_INSTALL: "https://trkapi.impact.com/AppInstall",
  CONVERSION_SUFFIX: "/Conversions.json",
};

/**
 * Standard event types recognized by Impact
 */
const EVENT_TYPES = {
  APPLICATION_INSTALLED: "Application Installed",
  INSTALL: "INSTALL",
  ORDER_COMPLETE: "Order Complete",
  ORDER_COMPLETED: "Order Completed",
  PAGE_LOAD: "Page Load",
  SCREEN_LOAD: "Screen Load",
};

// Default values and constants
const DEFAULT_ORDER_ID = "IR_AN_64_TS";
const REFERRER_TYPE = "impactradius";
const INTEGRATION_SOURCE = "ChordCDPCustom";
const INTEGRATION_VERSION = "1.0.0";

/**
 * Impact destination plugin implementation
 * Handles sending analytics events to Impact's tracking APIs
 */
export const impactPlugin: InternalPlugin<ImpactDestinationCredentials> = {
  id: "impact",
  async handle(config, payload: AnalyticsClientEvent) {
    // Skip if event doesn't match filters
    if (!applyFilters(payload, config)) {
      return;
    }

    // Determine the Impact event type based on the incoming event
    const eventType = mapEventType(payload, config);
    if (!eventType) {
      console.warn("Undefined/Unmatched Event Type for Impact");
      return;
    }

    // Extract common data needed for all event types
    const commonData = await commonMapper(payload, config);

    // Handle different event types with appropriate endpoints and data
    switch (eventType) {
      case EVENT_TYPES.PAGE_LOAD:
      case EVENT_TYPES.SCREEN_LOAD:
        // Skip if the specific event type is disabled in config
        if (
          (eventType === EVENT_TYPES.PAGE_LOAD && !config.enablePageEvents) ||
          (eventType === EVENT_TYPES.SCREEN_LOAD && !config.enableScreenEvents)
        ) {
          console.warn(`${eventType} is not enabled for this event`);
          return;
        }

        // Send page/screen view to the PageLoad endpoint
        await fetch(ENDPOINTS.PAGE_LOAD, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(commonData),
        }).catch(e => {
          console.warn(`Impact ${eventType} tracking failed: ${e.message}`, e);
        });
        break;

      case EVENT_TYPES.ORDER_COMPLETE:
      case EVENT_TYPES.ORDER_COMPLETED:
        // For order events, get additional order data and send to Conversions endpoint
        const orderData = await getOrderData(payload, config, commonData);
        const orderFormData = createFormData(orderData);

        await fetch(`${ENDPOINTS.CONVERSION_PREFIX}/${config.accountSid}${ENDPOINTS.CONVERSION_SUFFIX}`, {
          method: "POST",
          headers: {
            Authorization: `Basic ${btoa(`${config.accountSid}:${config.apiKey}`)}`,
            "Content-Type": "application/x-www-form-urlencoded",
          },
          body: orderFormData,
        }).catch(e => {
          console.warn(`Impact order tracking failed: ${e.message}`, e);
        });
        break;

      case EVENT_TYPES.APPLICATION_INSTALLED:
        // For app install events, first get a click ID, then send to Conversions endpoint
        const appInstallData = await getAppInstallData(payload, config, commonData);
        const appInstallFormData = createFormData(appInstallData);

        await fetch(`${ENDPOINTS.CONVERSION_PREFIX}/${config.accountSid}${ENDPOINTS.CONVERSION_SUFFIX}`, {
          method: "POST",
          headers: {
            Authorization: `Basic ${btoa(`${config.accountSid}:${config.apiKey}`)}`,
            "Content-Type": "application/x-www-form-urlencoded",
          },
          body: appInstallFormData,
        }).catch(e => {
          console.warn(`Impact app install tracking failed: ${e.message}`, e);
        });
        break;

      default:
        console.warn(`Unsupported Impact event type: ${eventType}`);
    }
  },
};

/**
 * Maps Jitsu event types to Impact event types
 * Considers both standard events and custom event mappings from config
 *
 * @param payload - The analytics event
 * @param config - Impact destination configuration
 * @returns The mapped Impact event type or null if no match
 */
function mapEventType(payload: AnalyticsClientEvent, config: ImpactDestinationCredentials): string | null {
  const { type, event } = payload;

  // Define default event types for each category
  const defaultInstallEvents = [EVENT_TYPES.APPLICATION_INSTALLED];
  const defaultOrderEvents = [EVENT_TYPES.ORDER_COMPLETE, EVENT_TYPES.ORDER_COMPLETED];
  const defaultPageEvents = [EVENT_TYPES.PAGE_LOAD];
  const defaultScreenEvents = [EVENT_TYPES.SCREEN_LOAD];

  // Check if event is in install events (with custom mapping support)
  if (findInCollection(event, config.installEventNames, defaultInstallEvents)) {
    return EVENT_TYPES.APPLICATION_INSTALLED;
  }

  // Check if event is in order events (with custom mapping support)
  if (findInCollection(event, config.actionEventNames, defaultOrderEvents)) {
    return EVENT_TYPES.ORDER_COMPLETE;
  }

  // Check for page/screen view events
  if (type === "page" || findInCollection(event, config.pageLoadEventNames, defaultPageEvents)) {
    return EVENT_TYPES.PAGE_LOAD;
  }

  if (type === "screen" || findInCollection(event, config.screenLoadEventNames, defaultScreenEvents)) {
    return EVENT_TYPES.SCREEN_LOAD;
  }

  // Check for identify events
  if (type === "identify" && config.enableIdentifyEvents) {
    return EVENT_TYPES.PAGE_LOAD; // Impact treats identify events as page loads
  }

  return null;
}

/**
 * Helper function to check if a value exists in a collection
 * Combines default values with custom values from config
 *
 * @param value - The value to find
 * @param collection - Custom collection from config
 * @param defaultValues - Default values to include
 * @returns True if value is found in the combined collection
 */
function findInCollection(
  value: string | undefined,
  collection: string[] | undefined,
  defaultValues: string[]
): boolean {
  if (!value) return false;

  const normalizedValue = String(value).toLowerCase();
  const combinedCollection = [...(collection || []), ...defaultValues];

  return combinedCollection.some(item => String(item).toLowerCase() === normalizedValue);
}

/**
 * Extracts and maps common data needed for all Impact events
 *
 * @param payload - The analytics event
 * @param config - Impact destination configuration
 * @returns Object with mapped common data
 */
async function commonMapper(
  payload: AnalyticsClientEvent,
  config: ImpactDestinationCredentials
): Promise<Record<string, any>> {
  const extractedData = extractEventData(payload);
  const { user, page, app } = extractedData;
  const { context = {}, properties = {}, timestamp } = payload;
  let { device } = context as AnalyticsContext;

  // Build base data object with common fields
  const data: Record<string, any> = {
    CampaignId: config.campaignId,
    PageUrl: page?.url || properties.url || page?.referrer,
    ReferringUrl: page?.referrer,
    EventDate: timestamp,
    CustomProfileId: user.anonymousId,
    CustomerId: user.id,
    CustomerEmail: user.email,
    IpAddress: user.ip,
    AppName: app.name,
    AppPackage: app.namespace,
    AppVer: app.version,
    DeviceMfr: user.deviceManufacturer,
    DeviceModel: user.deviceModel,
    DeviceCarrier: user.deviceCarrier,
    DeviceOs: user.deviceOs,
    DeviceOsVer: user.deviceOsVersion,
    DeviceLocale: user.locale,
    UserAgent: user.userAgent,
    Latitude: user.deviceLatitude,
    Longitude: user.deviceLongitude,
    IntegrationSource: INTEGRATION_SOURCE,
    IntegrationVersion: INTEGRATION_VERSION,
  };

  // Add ClickId if available and from Impact
  if ((context.referrer as any)?.id && String((context.referrer as any)?.type).toLowerCase() === REFERRER_TYPE) {
    data.ClickId = (context.referrer as any)?.id;
  }

  // Add device-specific data based on platform
  if (user.deviceType) {
    if (user.deviceType.toLowerCase() === "ios") {
      data.PropertyId = config.iOsAppId;
      data.AppleIfa = device.advertisingId;
      data.AppleAdTrack = device.adTrackingEnabled;
      data.AppleIfv = device.id;
    } else if (user.deviceType.toLowerCase() === "android") {
      data.PropertyId = config.androidAppId;
      data.GoogAId = device.advertisingId;
      data.AppleAdTrack = device.adTrackingEnabled;
    }
  }

  // Add custom parameters from configuration
  if (typeof config.customParameterMapping === "object") {
    for (const key in config.customParameterMapping) {
      if (!config.customParameterMapping[key]) continue;

      const value = findNestedValue(config.customParameterMapping[key], payload);
      if (value !== undefined && value !== null && value !== "") {
        data[key] = value;
      }
    }
  }

  // Hash email for privacy if present
  if (data.CustomerEmail) {
    data.CustomerEmail = await hashEmail(data.CustomerEmail);
  }

  return data;
}

/**
 * Extracts and maps order data for conversion events
 *
 * @param payload - The analytics event
 * @param config - Impact destination configuration
 * @param commonData - Common data already extracted
 * @returns Object with order data for Impact
 */
async function getOrderData(
  payload: AnalyticsClientEvent,
  config: ImpactDestinationCredentials,
  commonData: Record<string, any>
): Promise<Record<string, any>> {
  const extractedData = extractEventData(payload);
  const { order } = extractedData;
  const { context = {} } = payload;
  const { traits = {} } = context;

  // Map product data from the order
  const productData = mapProductData(payload, config.customMappingForProducts || {});

  // Combine common data with order-specific data
  const orderData = {
    ...commonData,
    ...productData,
    OrderId: order?.id || order?.orderId || getOrderId(payload),
    CustomerStatus: traits.status,
    EventTypeCode: payload.event ? String(payload.event).replace(",", "") : null,
    EventTypeId: config.eventTypeId ? parseInt(config.eventTypeId, 10) : null,
  };

  return orderData;
}

/**
 * Extracts and maps app install data for app install events
 * First gets a click ID from Impact, then prepares data for conversion tracking
 *
 * @param payload - The analytics event
 * @param config - Impact destination configuration
 * @param commonData - Common data already extracted
 * @returns Object with app install data for Impact
 */
async function getAppInstallData(
  payload: AnalyticsClientEvent,
  config: ImpactDestinationCredentials,
  commonData: Record<string, any>
): Promise<Record<string, any>> {
  // First, get a click ID from the app install endpoint
  let clickId: string | undefined;
  try {
    const response = await fetch(ENDPOINTS.APPLICATION_INSTALL, {
      method: "POST",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: createFormData(commonData),
    });

    const responseData = await response.json();
    if (responseData.clickId) {
      clickId = responseData.clickId;
    } else if (responseData.landingPage) {
      clickId = findIrClickId(responseData.landingPage);
    }
  } catch (e) {
    console.warn(`Failed to get Impact click ID: ${e.message}`, e);
  }

  // Combine common data with app install specific data
  const appInstallData = {
    ...commonData,
    ClickId: clickId,
    EventTypeId: payload.context?.device?.type === "ios" ? config.iOsAppId : config.androidAppId,
    EventCode: EVENT_TYPES.INSTALL,
  };

  return appInstallData;
}

/**
 * Maps product data from an analytics event to Impact's format
 * Handles both standard and custom product field mappings
 *
 * @param payload - The analytics event
 * @param customMapping - Custom field mappings for products
 * @returns Object with mapped product data
 */
function mapProductData(payload: AnalyticsClientEvent, customMapping: Record<string, string>): Record<string, any> {
  const { properties = {} } = payload;
  const extractedProducts = extractProducts(payload);
  const productData: Record<string, any> = {};

  // Standard mapping for order-level properties
  const ORDER_KEY_MAPPING = {
    coupon: "OrderPromoCode",
    currency: "CurrencyCode",
    discount: "OrderDiscount",
    shipping: "OrderShipping",
    tax: "OrderTax",
  };

  // Map order-level properties first
  for (const key in properties) {
    if (ORDER_KEY_MAPPING[key] && key !== "total") {
      productData[ORDER_KEY_MAPPING[key]] = properties[key];
    }
  }

  // Standard mapping for item-level properties
  const ITEM_KEY_MAPPING = {
    brand: "ItemBrand",
    category: "ItemCategory",
    coupon: "ItemPromoCode",
    name: "ItemName",
    price: "ItemPrice",
    quantity: "ItemQuantity",
    sku: "ItemSku",
  };

  // Create merged mapping with custom overrides
  const mergedMapping: Record<string, string> = {};
  let priceKey = "price"; // Track which field contains price information

  // Apply standard mappings first, then override with custom mappings
  for (const key in ITEM_KEY_MAPPING) {
    if (customMapping[ITEM_KEY_MAPPING[key]]) {
      if (ITEM_KEY_MAPPING[key] === "ItemPrice") {
        priceKey = customMapping[ITEM_KEY_MAPPING[key]];
      }
      mergedMapping[customMapping[ITEM_KEY_MAPPING[key]]] = ITEM_KEY_MAPPING[key];
    } else {
      mergedMapping[key] = ITEM_KEY_MAPPING[key];
    }
  }

  // Add any additional custom mappings
  for (const key in customMapping) {
    if (key === "ItemPrice") {
      priceKey = customMapping[key];
    }
    mergedMapping[customMapping[key]] = key;
  }

  // Map each product in the products array
  for (let i = 0; i < extractedProducts.length; i++) {
    const product = extractedProducts[i];

    // Map standard product properties with index suffix
    productData[`ItemBrand${i}`] = product.brand;
    productData[`ItemCategory${i}`] = product.category;
    productData[`ItemPromoCode${i}`] = product.coupon;
    productData[`ItemName${i}`] = product.name;
    productData[`ItemPrice${i}`] = product.price;
    productData[`ItemQuantity${i}`] = product.quantity;
    productData[`ItemSku${i}`] = product.sku;

    // Apply any custom mappings to product properties
    for (const key in product) {
      if (mergedMapping[key]) {
        productData[`${mergedMapping[key]}${i}`] = product[key];
      }
    }

    // Use subtotal as fallback for price if needed
    if (!productData[priceKey] && product.subtotal) {
      productData[`ItemPrice${i}`] = product.subtotal;
    }
  }

  const extractedOrder = extractOrder(payload);
  if (!productData[`ItemPrice0`]) {
    const revenue = extractedOrder?.revenue || extractedOrder?.total || properties.revenue || properties.total;
    if (revenue) {
      productData.OrderSubTotalPostDiscount = revenue;
    }
  }

  return productData;
}

/**
 * Extracts order ID from various possible sources in the event
 * Falls back to a default if no order ID is found
 *
 * @param payload - The analytics event
 * @returns The order ID as a string
 */
function getOrderId(payload: AnalyticsClientEvent): string {
  const { properties = {}, messageId } = payload;
  const { order } = extractEventData(payload);

  // Try multiple possible sources for order ID
  const id =
    properties.orderId ??
    properties.order_id ??
    properties.transactionID ??
    properties.messageId ??
    messageId ??
    order?.id ??
    order?.orderId ??
    DEFAULT_ORDER_ID;

  return String(id);
}

/**
 * Extracts Impact click ID from a URL query string
 *
 * @param url - The URL that might contain an Impact click ID
 * @returns The click ID if found, undefined otherwise
 */
function findIrClickId(url: string): string | undefined {
  const queryString = (url || "").split("?");
  if (!queryString[1]) {
    return undefined;
  }

  // Parse query parameters to find irclickid
  const pairs = queryString[1].split("&");
  for (let i = 0; i < pairs.length; i++) {
    const [key, value] = pairs[i].split("=");
    if (key === "irclickid") {
      try {
        return decodeURIComponent(value);
      } catch (e) {
        return value;
      }
    }
  }

  return undefined;
}

/**
 * Converts an object to URL-encoded form data
 *
 * @param data - Object with key-value pairs to encode
 * @returns URL-encoded string suitable for form submission
 */
function createFormData(data: Record<string, any>): string {
  return Object.keys(data)
    .filter(key => data[key] !== null && typeof data[key] !== "undefined")
    .map(key => encodeURIComponent(key) + "=" + encodeURIComponent(data[key]))
    .join("&");
}

/**
 * Finds a value in a nested object structure using dot notation
 *
 * @param key - Dot-notation path to the desired value (e.g., "context.traits.email")
 * @param source - The object to search within
 * @returns The value if found, null otherwise
 */
function findNestedValue(key: string, source: any): any {
  const keyParts = key.split(".");
  let currentValue = source;

  // Traverse the object following the path
  for (let i = 0, count = keyParts.length; i < count; ++i) {
    if (typeof currentValue !== "object" || currentValue === null) {
      return null;
    }
    currentValue = currentValue[keyParts[i]];
  }

  return currentValue;
}

/**
 * Creates a SHA-1 hash of an email address for privacy
 * Uses browser's SubtleCrypto API if available, falls back to simple hash
 *
 * @param email - The email address to hash
 * @returns A promise that resolves to a hashed representation of the email
 */
async function hashEmail(email: string): Promise<string> {
  if (!email) return email;

  try {
    const normalizedEmail = email.trim().toLowerCase();

    if (typeof window !== "undefined" && window.crypto && window.crypto.subtle) {
      try {
        const encoder = new TextEncoder();
        const data = encoder.encode(normalizedEmail);
        const hashBuffer = await window.crypto.subtle.digest("SHA-1", data);

        const hashArray = Array.from(new Uint8Array(hashBuffer));
        const hashHex = hashArray.map(b => b.toString(16).padStart(2, "0")).join("");
        return hashHex;
      } catch (subtleError) {
        console.warn("SubtleCrypto failed, falling back to simple hash:", subtleError);
        return createSHA1Hash(normalizedEmail);
      }
    } else {
      // Fallback to simple hash for older browsers
      return createSHA1Hash(normalizedEmail);
    }
  } catch (e) {
    console.warn("Failed to hash email, using cleartext:", e);
    return email;
  }
}

/**
 * Creates a SHA-1-like hash in a browser environment
 * This is a simplified implementation that produces consistent results
 */
function createSHA1Hash(input: string): string {
  // Simple implementation that produces a hex string similar to SHA-1
  let hash = 0;
  if (input.length === 0) return hash.toString(16);

  for (let i = 0; i < input.length; i++) {
    const char = input.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32bit integer
  }

  // Convert to hex and pad to make it look more like a proper hash
  const hexHash = Math.abs(hash).toString(16);
  return hexHash.padStart(8, "0");
}
