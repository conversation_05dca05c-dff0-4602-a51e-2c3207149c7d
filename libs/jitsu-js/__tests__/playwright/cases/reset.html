<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>Tracking page</title>
    <script>
      window.testOnload = async j => {
        await j.setAnonymousId("john-doe-id-1");
        await j.identify("john-nondoe", { email: "<EMAIL>" });
        await j.identify("john-nondoe", { email: "<EMAIL>", $doNotSend: true });
        await j.track("pageLoaded", { trackParam: "trackValue" });
        await j.reset();
        await j.track("pageLoaded", { trackParam: "trackValue" });
      };
    </script>
    <script
      type="text/javascript"
      src="<%=trackingBase%>/p.js"
      data-onload="testOnload"
      data-debug="true"
      data-init-only="true"
      defer
    ></script>
  </head>

  <body>
    <h1>Test</h1>
  </body>
</html>
