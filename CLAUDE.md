# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Jitsu is an open-source, self-hosted event collection platform - an alternative to Segment. It collects event data from websites and apps and streams them to data warehouses or other services. The codebase is organized as a monorepo with multiple packages and services.

## Essential Commands

### Development Environment
- `pnpm install` - Install dependencies
- `pnpm dev` - Start development environment (runs console and rotor services)
- `pnpm console:dev` - Start only the console (Next.js app) with turbopack
- `pnpm rotor:dev` - Start only the rotor service (functions server)
- `pnpm ee-api:dev` - Start enterprise API service

### Building and Testing
- `pnpm build` - Build all packages
- `pnpm test` - Run all tests
- `pnpm lint` - Run linter across all packages
- `pnpm format` - Format code with Prettier (only changed files)
- `pnpm format:all` - Format all files with Prettier
- `pnpm ci:all` - Full CI pipeline (format check, lint, build, test)

### Database Operations (Console)
- `pnpm console:db-prepare` - Update database schema
- `pnpm console:db-prepare-force` - Force update database schema (accepts data loss)

### Utility Commands
- `pnpm factory-reset` - Clean everything and reset environment
- `pnpm clean` - Clean build artifacts
- `pnpm jitsu-cli` - Run the Jitsu CLI tool

## Architecture Overview

This is a **monorepo** using **pnpm workspaces** and **Turbo** for build orchestration:

### Core Services
- **Console** (`webapps/console/`) - Next.js web application for managing Jitsu configurations
- **Rotor** (`services/rotor/`) - Functions server that processes events and runs user-defined functions
- **EE API** (`webapps/ee-api/`) - Enterprise API service for additional features
- **Profiles** (`services/profiles/`) - Profile management service

### Libraries
- **JS SDK** (`libs/jitsu-js/`) - Browser and Node.js SDK for event collection
- **React SDK** (`libs/jitsu-react/`) - React-specific SDK with hooks and providers
- **Core Functions** (`libs/core-functions/`) - Built-in destination functions and utilities
- **Event Extractors** (`libs/event-extractors/`) - Event processing and extraction utilities
- **Functions Library** (`libs/functions/`) - Shared function utilities

### CLI Tools
- **Jitsu CLI** (`cli/jitsu-cli/`) - Command-line tool for function development and deployment
- **Build Scripts** (`cli/build-scripts/`) - Build and deployment utilities

### Key Technologies
- **Frontend**: Next.js, React, Ant Design, Tailwind CSS
- **Backend**: Node.js, Express, TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Message Queue**: Kafka
- **Caching**: Redis
- **Build System**: Turbo monorepo, pnpm workspaces

## Development Requirements

- Node.js 18.x
- pnpm >= 8.2.0
- Docker >= 19.03.0 (for local development environment)

## Testing

The project uses Jest for testing. Individual packages may have their own test configurations:
- Run tests with `pnpm test`
- Tests are located in `__tests__` or `*.test.ts` files
- Some packages have integration tests using Playwright

## Important Notes

- Always run `pnpm format:check` before committing
- The project uses TypeScript throughout
- Environment variables are managed through `.env.local` files
- Database schema changes require running `pnpm console:db-prepare`
- The project includes both open-source and enterprise features