{"name": "jitsu-cli", "version": "0.0.0", "description": "", "author": "Jitsu Dev Team <<EMAIL>>", "publishConfig": {"access": "public"}, "bin": "./bin/jitsu-cli", "license": "MIT", "private": false, "scripts": {"clean": "rm -rf ./dist", "compile": "tsc -p . ", "build": "pnpm compile && webpack", "run": "pnpm build && node dist/main.js", "login": "pnpm build && node dist/main.js login", "init child": "pnpm build && node dist/main.js init", "build child": "pnpm build && node dist/main.js build", "run child": "pnpm build && node dist/main.js run -e ./data/page.json", "test child": "pnpm build && node dist/main.js test", "deploy child": "pnpm build && node dist/main.js deploy"}, "dependencies": {"figlet": "^1.6.0", "jest-cli": "^29.7.0", "tslib": "^2.6.3", "typescript": "^5.6.3"}, "devDependencies": {"@babel/preset-env": "^7.23.2", "@babel/preset-typescript": "^7.23.2", "@jitsu/functions-lib": "workspace:*", "@jitsu/protocols": "workspace:*", "@rollup/plugin-commonjs": "^28.0.2", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-terser": "^0.4.3", "@rollup/plugin-typescript": "^11.1.3", "@types/chalk": "^2.2.0", "@types/commander": "^2.12.2", "@types/express": "^4.17.21", "@types/inquirer": "^9.0.3", "@types/jest": "^29.5.5", "@types/lodash": "^4.14.198", "@types/node": "^18.15.3", "@types/webpack": "^5.28.5", "@webpack-cli/generators": "^3.0.7", "babel-loader": "^9.1.3", "chalk": "^5.3.0", "commander": "^11.0.0", "cross-fetch": "^4.0.0", "cuid": "^2.1.8", "etag": "^1.8.1", "inquirer": "^9.2.11", "jest": "^29.7.0", "json5": "^2.2.3", "juava": "workspace:*", "lodash": "^4.17.21", "node-fetch": "^3.3.2", "node-loader": "^2.0.0", "prismjs": "^1.30.0", "rollup": "^3.29.5", "semver": "^7.5.4", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "webpack": "^5.99.5", "webpack-cli": "^6.0.1"}}