import type { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { FullContext } from "@jitsu/protocols/functions";
import _ from "lodash";
import type { KlaviyoCredentials } from "../../../meta";
import { API_URL } from "../config";
import { buildHeaders, createEventData } from "../functions";
import { KlaviyoEventData } from "../types";

export const orderCompleted = async (event: AnalyticsServerEvent, ctx: FullContext) => {
  const eventData = createEventData(event);
  const props = ctx.props as KlaviyoCredentials;

  const result = await ctx.fetch(`${API_URL}/events`, {
    method: "POST",
    headers: buildHeaders(props.apiKey),
    body: JSON.stringify(eventData),
  });

  if (result.status !== 202) {
    throw new Error(`
      Klaviyo Post failed for event ${event.event}
      HTTP Request URL: ${API_URL}/events
      ---
      ${eventData && `Payload: ${JSON.stringify(eventData)}`}
      ---
      Reuslt Status: ${result.status}
      ---
      Reuslt Text: ${await result.text()}
  `);
  }

  ctx.log.debug(`Klaviyo: ${result.status} ${await result.text()}`);

  if (result.status === 202 && Array.isArray(event?.properties?.products)) {
    await sendProductRequests(eventData, props, ctx);
  }

  return result;
};

const sendProductRequests = async (orderEventData: KlaviyoEventData, props: KlaviyoCredentials, ctx: FullContext) => {
  const products = orderEventData?.data?.attributes?.properties?.products;

  if (!products || !Array.isArray(products)) return;

  delete orderEventData.data.attributes.properties?.products;

  const productPromises = products.map(product => {
    const productEventData = {
      data: {
        type: "event",
        attributes: {
          properties: { ...orderEventData.data.attributes.properties, ...product },
          unique_id: _.uniqueId(),
          metric: {
            data: {
              type: "metric",
              attributes: {
                name: "Ordered Product",
              },
            },
          },
          time: orderEventData.data.attributes.time,
          profile: orderEventData.data.attributes.profile,
        },
      },
    };

    return ctx.fetch(`${API_URL}/events`, {
      method: "POST",
      headers: buildHeaders(props.apiKey),
      body: JSON.stringify(productEventData),
    });
  });

  await Promise.all(productPromises);
};
