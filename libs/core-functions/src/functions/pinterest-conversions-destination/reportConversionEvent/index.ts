import type { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { validate, parseFql } from "@segment/destination-subscriptions";
import { getDestinationByIdOrKey } from "@segment/action-destinations";
import { FullContext } from "@jitsu/protocols/functions";
import type { PinterestConversionsCredentials } from "../../../meta";
import _ from "lodash";
import { customMapping } from "../customMapping";

/**
 * Sends tracking event data to Pinterest Conversions API
 * @param event Analytics event to be processed
 * @param ctx Function execution context
 * @returns Segment Result[] object
 */
export const reportConversionEvent = async (event: AnalyticsServerEvent, ctx: FullContext) => {
  try {
    const props = ctx.props as PinterestConversionsCredentials;
    const destination = await getDestinationByIdOrKey("actions-pinterest-conversions-api");

    if (!destination) {
      throw new Error("Pinterest destination not found");
    }

    const action = destination.actions["reportConversionEvent"];

    // Find matching preset using Segment's FQL syntax
    const eventPreset =
      destination.definition.presets?.find(
        preset =>
          (preset["name"] == "Checkout" && event.event === "Order Completed") ||
          validate(parseFql(preset["subscribe"]), event)
      ) ?? null;

    if (!eventPreset) {
      ctx.log.debug("No matching Pinterest partner event found", { event });
      return [];
    }

    // Configure Pinterest API settings
    const settings = {
      ad_account_id: props.adAccountId,
      conversion_token: props.conversionToken,
      test_mode: props?.testMode ?? false,
    };

    // Get default mapping from the preset
    const defaultMapping = eventPreset.mapping || {};

    // Merge default mapping with custom mapping logic
    const mapping = _.merge({}, defaultMapping, customMapping(event, props.appName));

    // Execute the Pinterest API call
    const response = await action.execute({
      mapping,
      data: event,
      settings,
      auth: { accessToken: "", refreshToken: "" }, // Pinterest uses conversion token instead
    });

    ctx.log.info("Pinterest event tracking successful", { response, event });
    return response;
  } catch (error) {
    ctx.log.error("Pinterest event tracking failed", { error, event });
    throw error;
  }
};
