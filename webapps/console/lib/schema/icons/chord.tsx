export const ChordIconBlack = (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" version="1.1">
    <g>
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M23.9662 6.56292C23.9662 6.56297 23.9661 6.56301 23.9661 6.56306L20.9849 9.54914C26.098 7.785 31.9631 8.82173 36.1971 12.6624L42.8147 6.03416C37.4286 1.20091 29.1443 1.37912 23.9662 6.56292ZM19.2441 11.2928L16.2621 14.2795C16.261 14.2807 16.2599 14.2819 16.2587 14.2831C10.9046 19.6523 10.9035 28.3528 16.2619 33.7172C21.6196 39.0811 30.3029 39.0858 35.6604 33.725C35.6628 33.7225 35.6653 33.72 35.6678 33.7175L38.6499 30.7306C33.3063 32.5747 27.1445 31.3605 22.8806 27.0876C18.6168 22.8145 17.4046 16.6428 19.2441 11.2928ZM43.3721 26.0005L44.4575 27.0877L36.7534 34.8044C36.7511 34.8068 36.7488 34.809 36.7464 34.8114L29.0463 42.524C29.0463 42.5242 29.0462 42.5242 29.0462 42.5242C23.0894 48.4937 13.4287 48.4901 7.46967 42.5244C1.51003 36.5579 1.51023 26.8814 7.46954 20.9123L15.1735 13.1956C15.1746 13.1947 15.1755 13.1937 15.1764 13.1927L22.881 5.47563C28.8401 -0.490197 38.5009 -0.49365 44.4576 5.47591L45 6.01948L36.2106 14.8233L35.6679 14.2798C31.678 10.2833 25.8421 9.26079 20.9085 11.2163C18.9565 16.1581 19.9782 22.0039 23.9663 26.0007C29.3233 31.3691 38.012 31.3693 43.3721 26.0005ZM11.5417 19.0077L8.55492 21.9994C3.1949 27.3681 3.19509 36.0712 8.55478 41.4369C13.915 46.8032 22.6039 46.8054 27.9607 41.4372L30.9454 38.4475C25.6031 40.2901 19.4419 39.0746 15.1768 34.8046C10.9102 30.5332 9.69975 24.3605 11.5417 19.0077Z"
        fill="black"
      />
    </g>
  </svg>
);

export const ChordIconWhite = (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" version="1.1">
    <g>
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M23.9662 6.56292C23.9662 6.56297 23.9661 6.56301 23.9661 6.56306L20.9849 9.54914C26.098 7.785 31.9631 8.82173 36.1971 12.6624L42.8147 6.03416C37.4286 1.20091 29.1443 1.37912 23.9662 6.56292ZM19.2441 11.2928L16.2621 14.2795C16.261 14.2807 16.2599 14.2819 16.2587 14.2831C10.9046 19.6523 10.9035 28.3528 16.2619 33.7172C21.6196 39.0811 30.3029 39.0858 35.6604 33.725C35.6628 33.7225 35.6653 33.72 35.6678 33.7175L38.6499 30.7306C33.3063 32.5747 27.1445 31.3605 22.8806 27.0876C18.6168 22.8145 17.4046 16.6428 19.2441 11.2928ZM43.3721 26.0005L44.4575 27.0877L36.7534 34.8044C36.7511 34.8068 36.7488 34.809 36.7464 34.8114L29.0463 42.524C29.0463 42.5242 29.0462 42.5242 29.0462 42.5242C23.0894 48.4937 13.4287 48.4901 7.46967 42.5244C1.51003 36.5579 1.51023 26.8814 7.46954 20.9123L15.1735 13.1956C15.1746 13.1947 15.1755 13.1937 15.1764 13.1927L22.881 5.47563C28.8401 -0.490197 38.5009 -0.49365 44.4576 5.47591L45 6.01948L36.2106 14.8233L35.6679 14.2798C31.678 10.2833 25.8421 9.26079 20.9085 11.2163C18.9565 16.1581 19.9782 22.0039 23.9663 26.0007C29.3233 31.3691 38.012 31.3693 43.3721 26.0005ZM11.5417 19.0077L8.55492 21.9994C3.1949 27.3681 3.19509 36.0712 8.55478 41.4369C13.915 46.8032 22.6039 46.8054 27.9607 41.4372L30.9454 38.4475C25.6031 40.2901 19.4419 39.0746 15.1768 34.8046C10.9102 30.5332 9.69975 24.3605 11.5417 19.0077Z"
        fill="white"
      />
    </g>
  </svg>
);
