import { ExtractedEventData } from "@jitsu/event-extractors/src/index";

export function formatPhone(phone: string | undefined): string | undefined {
  if (!phone) return undefined;

  // Remove spaces and non-digits; append + to the beginning
  let formattedPhone = `+${phone.replace(/[^0-9]/g, "")}`;

  // Limit length to 15 characters
  formattedPhone = formattedPhone.substring(0, 15);

  return formattedPhone;
}

export function formatString(str: string | undefined | null): string | undefined {
  if (!str) return undefined;
  return str.replace(/\s/g, "").toLowerCase();
}

export function formatAddress(address: string | undefined | null): string | undefined {
  if (!address) return undefined;
  return address.replace(/[^A-Za-z0-9]/g, "").toLowerCase();
}

export function createEventProperties(eventData: ExtractedEventData, properties: any) {
  const { order } = eventData;

  const eventProps: any = {
    contents: [],
    content_type: "product",
    currency: order.currency || "USD",
    value: order.value || order.total,
    description: order.products ? order.products[0]?.description : undefined,
    query: properties.query,
    order_id: order.orderId || order.id,
    shop_id: properties.shop_id,
  };

  // Handle products array
  if (order.products) {
    eventProps.contents = order.products.map(product => ({
      price: product.price,
      quantity: product.quantity,
      content_category: product.category,
      content_id: product.sku || product.product_id || product.id,
      content_name: product.name,
      brand: product.brand,
    }));
  }

  // Remove undefined values
  Object.keys(eventProps).forEach(key => {
    if (eventProps[key] === undefined) {
      delete eventProps[key];
    }
  });

  return eventProps;
}
