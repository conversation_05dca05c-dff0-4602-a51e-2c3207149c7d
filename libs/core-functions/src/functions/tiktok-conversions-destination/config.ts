export const API_URL: string = "https://business-api.tiktok.com/open_api/v1.3";

export const eventPresetMappings = {
  "Order Completed": "CompletePayment",
  "Callback Started": "Contact",
  "Subscription Created": "Subscribe",
  "Form Submitted": "SubmitForm",
  Page: "PageView",
  "Product Viewed": "ViewContent",
  "Product Clicked": "ClickButton",
  "Products Searched": "Search",
  "Product Added to Wishlist": "AddToWishlist",
  "Product Added": "AddToCart",
  "Checkout Started": "InitiateCheckout",
  "Payment Info Entered": "AddPaymentInfo",
  "Order Placed": "PlaceOrder",
  "Download Link Clicked": "Download",
  "Account Created": "CompleteRegistration",
};

export const eventPresetOptions: string[] = Object.keys(eventPresetMappings).sort();

// Define event groups
export const singleProductEvents = [
  "Product Added",
  "Product Viewed",
  "Product Clicked",
  "Products Searched",
  "Product Added to Wishlist",
];

export const multiProductEvents = ["Order Completed", "Checkout Started", "Payment Info Entered", "Order Placed"];
