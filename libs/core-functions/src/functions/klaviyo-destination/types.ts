// @see https://developers.klaviyo.com/en/reference/create_event
export interface KlaviyoEventData {
  data: {
    type: string;
    attributes: {
      properties?: {
        products?: [
          {
            [key: string]: any;
          }
        ];
      };
      time?: string | number;
      unique_id?: string;
      value?: number;
      metric: {
        data: {
          type: string;
          attributes: {
            name?: string;
          };
        };
      };
      profile: KlaviyoProfileData;
    };
  };
}

// @see https://developers.klaviyo.com/en/reference/create_profile
export interface KlaviyoProfileData {
  data: {
    type: string;
    id?: string;
    attributes: KlaviyoProfileAttributes;
  };
}

export interface KlaviyoProfileAttributes {
  email?: string;
  phone_number?: string;
  external_id?: string;
  anonymous_id?: string;
  first_name?: string;
  last_name?: string;
  locale?: string;
  location?: {
    address1?: string;
    address2?: string;
    city?: string;
    region?: string;
    zip?: string;
    country?: string;
    ip?: string;
  };
}
