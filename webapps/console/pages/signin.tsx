import { getCsrfToken, signIn, useSession } from "next-auth/react";
import { Redirect } from "../components/Redirect/Redirect";
import { Button, Input } from "antd";
import { useAppConfig } from "../lib/context";
import { AlertTriangle } from "lucide-react";
import Link from "next/link";
import { GithubOutlined, KeyOutlined, GoogleOutlined } from "@ant-design/icons";
import React, { useState } from "react";
import { feedbackError } from "../lib/ui";
import { useRouter } from "next/router";
import { branding } from "../lib/branding";
import {
  credentialsLoginEnabled,
  githubLoginEnabled,
  oidcLoginEnabled,
  googleLoginEnabled,
} from "../lib/nextauth.config";
import { useQuery } from "@tanstack/react-query";

function JitsuLogo() {
  return (
    <div className="flex items-center w-fit h-full space-x-2">
      <div className="aspect-square h-full">{branding.logo}</div>
      <div className="text-textDark h-4/6">{branding.wordmark}</div>
    </div>
  );
}

function CredentialsForm() {
  const lastUsedLogin = localStorage.getItem("last-used-login-email") || undefined;
  const { data, isLoading, error } = useQuery(["next-auth-csrfToken"], async () => {
    return {
      csrfToken: await getCsrfToken(),
    };
  });
  const loading = isLoading || !!error;
  return (
    <form className="space-y-4" method="post" action={"/api/auth/callback/credentials"}>
      {data?.csrfToken && <input name="csrfToken" type="hidden" defaultValue={data?.csrfToken} />}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700" htmlFor="email">
          Email
        </label>
        <Input
          disabled={loading}
          id="email"
          size="large"
          placeholder="<EMAIL>"
          required
          type="email"
          name="username"
          defaultValue={lastUsedLogin}
          onChange={e => {
            if (e.target.value) {
              localStorage.setItem("last-used-login-email", e.target.value);
            }
          }}
        />
      </div>
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700" htmlFor="password">
          Password
        </label>
        <Input disabled={loading} id="password" size="large" required type="password" name="password" />
      </div>
      <Button htmlType="submit" className="w-full" type="primary" disabled={loading}>
        {loading ? "Preparing login form..." : "Sign In"}
      </Button>
    </form>
  );
}

function ProviderSignIn({ provider }) {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const providers = {
    github: {
      title: "GitHub",
      icon: <GithubOutlined />,
    },
    oidc: {
      title: "SSO",
      icon: <KeyOutlined />,
    },
    google: {
      title: "Google",
      icon: <GoogleOutlined />,
    },
  };

  return (
    <div className="space-y-4">
      <Button
        className="w-full"
        icon={providers[provider].icon}
        loading={loading}
        onClick={async () => {
          try {
            setLoading(true);
            await signIn(provider);
            await router.push("/");
          } catch (e: any) {
            feedbackError(`Failed to sign in with ${providers[provider].title}`, e);
          } finally {
            setLoading(false);
          }
        }}
      >
        Sign in with {providers[provider].title}
      </Button>
    </div>
  );
}

const NextAuthSignInPage = ({ csrfToken, providers: { github, oidc, credentials, google } }) => {
  const router = useRouter();
  const nextAuthSession = useSession();
  const app = useAppConfig();
  if (nextAuthSession.status === "authenticated") {
    return <Redirect href={"/"} />;
  }
  if (app.auth?.firebasePublic) {
    return (
      <div className="w-screen h-screen flex flex-col items-center justify-center">
        <AlertTriangle className="w-32 h-32 text-error" />
        <p className="mt-3 text-textLight text-center">
          This page should not be used if Firebase authorization is enabled. Please proceed to a{" "}
          <Link href="/" className="underline text-primary">
            main page of the app
          </Link>
        </p>
      </div>
    );
  }
  return (
    <div className="mx-auto max-w-[350px] space-y-6 pt-12">
      <div className="space-y-2 flex justify-center h-16">
        <JitsuLogo />
      </div>
      <div className="flex flex-col gap-1.5">
        {credentials.enabled && <CredentialsForm />}
        {credentials.enabled && (github.enabled || oidc.enabled) && <hr className="my-4" />}
        {github.enabled && <ProviderSignIn provider="github" />}
        {oidc.enabled && <ProviderSignIn provider="oidc" />}
        {google.enabled && <ProviderSignIn provider="google" />}
      </div>
      {router.query.error && (
        <p className="text-error">
          Something went wrong. Please try again. Error code: <code>{router.query.error}</code>
        </p>
      )}
      {!app.disableSignup && (github.enabled || oidc.enabled || google.enabled) && (
        <p className="text-center text-textLight text-xs">
          Automatic signup is enabled for this instance. Sign in with github and if you don't have an account, a new
          account will be created automatically. This account won't have any access to pre-existing project unless the
          access is explicitly granted
        </p>
      )}
    </div>
  );
};

export async function getServerSideProps() {
  if (process.env.FIREBASE_AUTH) {
    throw new Error(`Firebase auth is enabled. This page should not be used.`);
  }
  if (!githubLoginEnabled && !credentialsLoginEnabled && !oidcLoginEnabled && !googleLoginEnabled) {
    throw new Error(`No auth providers are enabled found. Available providers: github, credentials, OIDC, google`);
  }
  return {
    props: {
      providers: {
        credentials: {
          enabled: credentialsLoginEnabled,
          callbackUrl: "/api/auth/callback/credentials",
        },
        github: {
          enabled: githubLoginEnabled,
        },
        oidc: {
          enabled: oidcLoginEnabled,
        },
        google: {
          enabled: googleLoginEnabled,
        },
      },
      publicPage: true,
    },
  };
}

export default NextAuthSignInPage;
