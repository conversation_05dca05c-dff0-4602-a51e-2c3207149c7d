import { createHash } from "crypto";

/**
 * Checks if a string is already hashed (SHA256 format - 64 hex characters)
 */
const isHashedInformation = (information: string | null | undefined): boolean => {
  if (!information) return false;
  return new RegExp(/[0-9abcdef]{64}/gi).test(information);
};

/**
 * Convert emails to lower case, and hash in SHA256 if not already hashed
 */
export const formatEmails = (email_addresses: string[] | undefined): string[] => {
  return formatArray(email_addresses, email => formatValue(email, e => e.toLowerCase()));
};

/**
 * Convert string to match E.164 phone number pattern (e.g. +1234567890) and hash
 * Assumes input is a correctly formatted phone number (max 14 chars) with country code
 */
export const formatPhones = (phone_numbers: string[] | undefined): string[] => {
  if (!phone_numbers?.length) return [];

  return phone_numbers.map(phone => {
    if (isHashedInformation(phone)) {
      return phone;
    }

    // Format: remove non-digits, add + prefix, limit to 15 chars
    const formattedPhone = `+${phone.replace(/[^0-9]/g, "")}`.substring(0, 15);
    return hashAndEncode(formattedPhone);
  });
};

/**
 * Format and hash user IDs
 * @param userIds Array of user IDs to be formatted
 * @returns Array of hashed user IDs
 */
export function formatUserIds(userIds: string[] | undefined): string[] {
  return formatArray(userIds, userId => formatValue(userId, id => id.toLowerCase()));
}

/**
 * Format and hash a string value if not already hashed
 * @param str String to format
 * @returns Formatted/hashed string or empty string if input is empty
 */
export function formatString(str: string | undefined | null): string {
  if (!str) return "";

  if (!isHashedInformation(str)) {
    return hashAndEncode(str.replace(/\s/g, "").toLowerCase());
  }

  return str;
}

/**
 * Format an address by removing non-alphanumeric characters and converting to lowercase
 * @param address Address to format
 * @returns Formatted address or empty string if input is empty
 */
export function formatAddress(address: string | undefined | null): string {
  if (!address) return "";
  return address.replace(/[^A-Za-z0-9]/g, "").toLowerCase();
}
/**
 * Hashes a string using SHA256 algorithm
 */
function hashAndEncode(property: string): string {
  return createHash("sha256").update(property).digest("hex");
}

/**
 * Formats and hashes a string if not already hashed
 */
function formatValue(value: string, transformFn?: (value: string) => string): string {
  if (isHashedInformation(value)) {
    return value;
  }

  const transformedValue = transformFn ? transformFn(value) : value;
  return hashAndEncode(transformedValue);
}

/**
 * Processes an array of strings with a formatting function
 */
function formatArray(values: string[] | undefined, formatFn: (value: string) => string): string[] {
  if (!values?.length) return [];
  return values.map(formatFn);
}
