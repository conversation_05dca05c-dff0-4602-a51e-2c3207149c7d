import { AnalyticsClientEvent } from "@jitsu/protocols/analytics";
import { applyFilters, CommonDestinationCredentials, InternalPlugin } from "./index";
import { extractOrderData } from "@jitsu/event-extractors";

export type ShareASalePixelDestinationCredentials = {
  merchantId: string;
} & CommonDestinationCredentials;

declare global {
  interface Window {}
}

export const shareASalePixelPlugin: InternalPlugin<ShareASalePixelDestinationCredentials> = {
  id: "share-a-sale-pixel",
  async handle(config, payload: AnalyticsClientEvent) {
    if (!applyFilters(payload, config, "share-a-sale-pixel")) {
      return;
    }

    const { event } = payload;
    const orderData = extractOrderData(payload);

    const attributes = payload.properties?.attributes;
    let sscid = "";

    // find the sscid in the attributes
    if (Array.isArray(attributes)) {
      const sscidAttr = attributes.find(
        attr => typeof attr === "object" && attr !== null && "key" in attr && typeof (attr as any).key === "string"
      );
      if (sscidAttr && typeof (sscidAttr as any).value === "string") {
        sscid = (sscidAttr as any).value;
      }
    } else {
      console.warn("ShareASale Pixel: Attributes is not an array.");
      return;
    }

    if (event === "Order Completed") {
      function createUrl() {
        const merchantId = config.merchantId;
        const orderId = orderData.id;

        // ShareASale ignores conversion events without a sscid.
        // If we don't have one in the order we can return early.
        if (!sscid || !orderId || !merchantId) return false;

        const amount = orderData.total || 0;
        const currency = orderData.currency || "USD";
        const skuList = [];
        const quantityList = [];
        const priceList = [];

        orderData.products?.forEach(product => {
          skuList.push(product.sku);
          quantityList.push(product.quantity);
          priceList.push(product.price);
        });

        const data = {
          tracking: orderId,
          amount: amount.toFixed(2),
          merchantId,
          transType: "sale",
          sscid,
          currency,
          skulist: skuList.join(","),
          pricelist: priceList.join(","),
          quantitylist: quantityList.join(","),
        };

        try {
          const searchParams = new URLSearchParams(data)?.toString();
          return `https://www.shareasale.com/sale.cfm?${searchParams}`;
        } catch (error) {
          console.error(error);
          return false;
        }
      }

      const url = createUrl();

      if (!url) return;

      try {
        const response = await fetch(url);
        if (response.status === 200) {
          console.log(`ShareASale Pixel (merchantId=${config.merchantId}) successfully tracked order: ${orderData.id}`);
        }
      } catch (error) {
        console.log(`ShareASale Pixel (merchantId=${config.merchantId}) failed to track order: ${error}`);
      }
    }
  },
};
