import { loadScript } from "../script-loader";
import { AnalyticsClientEvent } from "@jitsu/protocols/analytics";
import { applyFilters, CommonDestinationCredentials, InternalPlugin } from "./index";
import { ExtractedOrderData, extractEventData } from "@jitsu/event-extractors";

export type FacebookPixelDestinationCredentials = {
  pixelId?: string;
} & CommonDestinationCredentials;

declare global {
  interface Window {
    fbq?: any;
    _fbq?: any;
  }
}

export const facebookPixelPlugin: InternalPlugin<FacebookPixelDestinationCredentials> = {
  id: "facebook-pixel",
  async handle(config, payload: AnalyticsClientEvent) {
    if (!applyFilters(payload, config, "facebook-pixel")) {
      return;
    }

    await initFacebookPixelIfNeeded(config, payload);

    const { pixelId } = config;
    const { order, eventType, messageId, eventName } = extractEventData(payload);

    const externalId = payload.userId || payload.anonymousId;
    const eventOptions = { eventID: messageId };
    if (externalId) {
      eventOptions["external_id"] = externalId;
    }

    switch (eventType) {
      case "page":
        window.fbq("track", "PageView", {}, eventOptions);
        break;
      case "track":
        switch (eventName) {
          case "Checkout Started":
            const checkoutStartedPayload = formatOrderData(order);
            window.fbq("trackSingle", pixelId, "InitiateCheckout", checkoutStartedPayload, eventOptions);
            break;

          case "Order Completed":
            const orderCompletedPayload = formatOrderData(order);
            window.fbq("trackSingle", pixelId, "Purchase", orderCompletedPayload, eventOptions);
            break;

          case "Product Added":
            const productAddedPayload = formatProductData(order);
            window.fbq("trackSingle", pixelId, "AddToCart", productAddedPayload, eventOptions);
            break;

          case "Product Viewed":
            const productViewedPayload = formatProductData(order);
            window.fbq("trackSingle", pixelId, "ViewContent", productViewedPayload, eventOptions);
            break;
        }
        break;
    }
  },
};

async function initFacebookPixelIfNeeded(config: FacebookPixelDestinationCredentials, payload: AnalyticsClientEvent) {
  if (getFacebookPixelState() !== "fresh") {
    return;
  }
  setFacebookPixelState("loading");

  window._fbq = function () {
    if (window.fbq.callMethod) {
      window.fbq.callMethod.apply(window.fbq, arguments);
    } else {
      window.fbq.queue.push(arguments);
    }
  };

  window.fbq = window.fbq || window._fbq;
  window.fbq.push = window.fbq;
  window.fbq.loaded = true;
  window.fbq.disablePushState = true; // disables automatic pageview tracking
  window.fbq.allowDuplicatePageViews = true; // enables fb
  window.fbq.version = "2.0";
  window.fbq.queue = [];

  await loadScript("https://connect.facebook.net/en_US/fbevents.js")
    .then(() => {
      setFacebookPixelState("loaded");

      const externalId = payload.userId || payload.anonymousId;
      if (externalId) {
        window.fbq("init", config.pixelId, { external_id: externalId });
      } else {
        window.fbq("init", config.pixelId);
      }
    })
    .catch(e => {
      console.warn(`Facebook Pixel (pixelId=${config.pixelId}) init failed: ${e.message}`, e);
      setFacebookPixelState("failed");
    });
}

type FacebookPixelState = "fresh" | "loading" | "loaded" | "failed";

function getFacebookPixelState(): FacebookPixelState {
  return window["__chordCDPFacebookPixelState"] || "fresh";
}

function setFacebookPixelState(s: FacebookPixelState) {
  window["__chordCDPFacebookPixelState"] = s;
}

function formatRevenue(revenue) {
  return Number(revenue || 0).toFixed(2);
}

const formatProductData = (order: ExtractedOrderData) => {
  if (!order?.products || order.products.length === 0) {
    return {};
  }

  const product = order.products[0];
  const id = product.sku || product.product_id || product.id;

  return {
    content_ids: id,
    content_type: "product",
    content_name: product.name,
    content_category: product.category,
    currency: order.currency,
    value: formatRevenue(product.price),
    contents: [
      {
        id: id,
        quantity: product.quantity || 1,
        item_price: product.price,
      },
    ],
  };
};

const formatOrderData = (order: ExtractedOrderData) => {
  if (!order?.products || order.products.length === 0) {
    return {};
  }

  const contentIds = order.products.map(product => product.sku || product.product_id || product.id).filter(Boolean);
  const orderValue = order.total ?? order.value;

  return {
    content_category: order.products[0]?.category,
    content_ids: contentIds,
    content_type: "product",
    contents: order.products.map(product => ({
      id: product.sku || product.product_id || product.id,
      quantity: product.quantity || 1,
      item_price: product.price,
    })),
    currency: order.currency,
    num_items: contentIds.length,
    value: formatRevenue(orderValue),
  };
};
