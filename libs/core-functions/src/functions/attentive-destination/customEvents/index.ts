import type { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { FullContext } from "@jitsu/protocols/functions";
import type { AttentiveCredentials } from "../../../meta";
import { API_URL } from "../config";
import { buildHeaders, createEventData } from "../functions";

export const customEvent = async (event: AnalyticsServerEvent, ctx: FullContext) => {
  const eventData = createEventData(event);
  const props = ctx.props as AttentiveCredentials;

  const result = await ctx.fetch(`${API_URL}/events/custom`, {
    method: "POST",
    headers: buildHeaders(props.apiKey),
    body: JSON.stringify(eventData),
  });

  if (result.status !== 200) {
    throw new Error(`Attentive: ${JSON.stringify(eventData)} --> ${result.status} ${await result.text()}`);
  }

  ctx.log.debug(`Attentive: ${result.status} ${await result.text()}`);

  return result;
};
