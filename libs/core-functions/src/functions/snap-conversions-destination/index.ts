import type { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { JitsuFunction } from "@jitsu/protocols/functions";
import { RetryError } from "@jitsu/functions-lib";
import { SnapConversionsCredentials } from "../../meta";
import { reportConversionEvent } from "./reportConversionEvent";

/**
 * @see https://github.com/segmentio/action-destinations/tree/main/packages/destination-actions/src/destinations/snap-conversions
 */
const SnapConversionsDestination: JitsuFunction<AnalyticsServerEvent, SnapConversionsCredentials> = async (
  event,
  ctx
) => {
  const isValidEvent = event.type === "page" || event.type === "track";

  if (isValidEvent) {
    try {
      return await reportConversionEvent(event, ctx);
    } catch (e: any) {
      throw new RetryError(e.message);
    }
  }
};

SnapConversionsDestination.displayName = "snap-conversions-destination";

SnapConversionsDestination.description =
  "This function sends events to Snap to measure and optimize Snap Ads performance.";

export default SnapConversionsDestination;
