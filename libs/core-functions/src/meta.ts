import { z } from "zod";
import { PropertyUI } from "./lib/meta-types";

const eventsParamDescription = `
List of events to send, delimited by comma. Following <code>page</code>, <code>screen</code>, or any arbitrary event (name of <code>track</code> event).
Special values: <b>empty string</b> - send only <code>track</code> events, <b>*</b> - send all events - this is useful if you want to filter events with Functions.
`;
export const FacebookConversionApiCredentials = z.object({
  pixelId: z.string().describe("Facebook Pixel ID"),
  accessToken: z.string().describe("Facebook Access Token"),
  actionSource: z
    .enum(["email", "website", "app", "phone_call", "chat", "physical_store", "system_generated", "other"])
    .default("website")
    .describe("Action Source"),
  events: z.string().optional().default("").describe(eventsParamDescription),
  phoneFieldName: z
    .string()
    .optional()
    .default("")
    .describe(
      "Name of the field in the event user traits that contains the phone number. Expected format could be <a href='https://en.wikipedia.org/wiki/E.164' target='_blank' rel='noreferrer noopener'>E.164</a> or international format. If empty, phone number hash will not be sent."
    ),
});

export const FacebookConversionApiCredentialsUi: Partial<Record<keyof FacebookConversionApiCredentials, PropertyUI>> = {
  accessToken: {
    password: true,
  },
};

export type FacebookConversionApiCredentials = z.infer<typeof FacebookConversionApiCredentials>;

export const WebhookDestinationConfig = z.object({
  url: z.string().url().describe("Webhook URL"),
  method: z
    .enum(["GET", "POST", "PUT", "DELETE"])
    .default("POST")
    .describe("HTTP method. Can be <code>GET</code>, <code>POST</code>, <code>PUT</code>, <code>DELETE</code>"),
  headers: z.array(z.string()).optional().describe("List of headers in format <code>key: value</code>"),
  customPayload: z
    .boolean()
    .optional()
    .default(false)
    .describe("Enable custom payload. If disabled, the event payload will be sent as is."),
  payload: z
    .string()
    .optional()
    .describe(
      "Payload Template::Template for the webhook payload. The following macros are supported:<ul><li><code>{{ EVENT }}</code> - event json object for stream mode or batches with size=1</li><li><code>{{ EVENTS }}</code> - for batch mode - json array of events</li><li><code>{{ EVENTS_COUNT }}</code> - count of events in batch</li><li><code>{{ NAME }}</code> - event name</li><li><code>{{ env.VAR_NAME }}</code> - value of VAR_NAME environment variable</li></ul>"
    ),
});

export type WebhookDestinationConfig = z.infer<typeof WebhookDestinationConfig>;

const MixpanelServiceAccountDocumentation =
  'See <a href="https://developer.mixpanel.com/reference/service-accounts">how to create service account</a>';

export const IntercomDestinationCredentials = z.object({
  accessToken: z
    .string()
    .describe(
      "Intercom Access Token. You should first create an app in Intercom Developer Hub, and then generate an access token in the app settings. See <a href='https://developers.intercom.com/docs/build-an-integration/getting-started/' target='_blank' rel='noreferrer noopener'>a detailed guide</a>"
    ),
  updateLastSeenOnEveryEvent: z
    .boolean()
    .optional()
    .describe(
      "By default, the last seen property will be updated only on .identify() calls. If enabled, the property will be updated on every event. However, enabling this option may lead to higher API usage."
    ),
});

export const IntercomDestinationCredentialsUi: Partial<
  Record<keyof z.infer<typeof IntercomDestinationCredentials>, PropertyUI>
> = {
  accessToken: {
    password: true,
  },
};

export type IntercomDestinationCredentials = z.infer<typeof IntercomDestinationCredentials>;

export const MixpanelCredentials = z.object({
  simplifiedIdMerge: z
    .boolean()
    .optional()
    .default(false)
    .describe(
      `Simplified Identity Merge::Use Mixpanel <a href="https://docs.mixpanel.com/docs/tracking-methods/identifying-users">Simplified Identity Merge</a> feature.<br/>Enable this option if your Mixpanel project has the corresponding <a href="https://docs.mixpanel.com/docs/tracking-methods/identifying-users#how-do-i-switch-between-the-simplified-and-original-api">feature enabled</a>.<br/><b>Using this feature is highly recommended to achieve better quality Identity Merge</b>`
    ),
  projectId: z
    .string()
    .describe(
      'Project id can be found in the <a href="https://help.mixpanel.com/hc/en-us/articles/************-Project-Settings">project settings</a>'
    ),
  projectToken: z
    .string()
    .describe('See <a href="https://developer.mixpanel.com/reference/project-token">how to obtain project secret</a>'),
  //apiSecret: z.string(),
  serviceAccountUserName: z.string().describe(MixpanelServiceAccountDocumentation),
  serviceAccountPassword: z.string().describe(MixpanelServiceAccountDocumentation),
  sendPageEvents: z
    .boolean()
    .optional()
    .default(true)
    .describe("If enabled, all page view events will be sent to Mixpanel."),
  sendIdentifyEvents: z
    .boolean()
    .optional()
    .default(false)
    .describe(
      "If enabled, any identify() call will send an Identify event to Mixpanel in addition to the profile update"
    ),
  enableGroupAnalytics: z
    .boolean()
    .optional()
    .default(false)
    .describe(
      "Mixpanel Group Analytics allows behavioral data analysis at a customized group level. Group Analytics is available as an add-on package to customers on <a href='https://mixpanel.com/pricing/' target='_blank' rel='noreferrer noopener'>Growth and Enterprise plans.</a>"
    ),
  filterBotTraffic: z.boolean().optional().default(true).describe("Don't send traffic from known bots to Mixpanel"),
  groupKey: z
    .string()
    .optional()
    .default("$group_id")
    .describe(
      "Group Key for Mixpanel Group Analytics. Make sure that Group Key in <a href='https://mixpanel.com/report' target='_blank' rel='noreferrer noopener'>Mixpanel project settings</a> matches the provided value."
    ),
  enableAnonymousUserProfiles: z
    .boolean()
    .optional()
    .default(false)
    .describe("If enabled, anonymous users will be tracked in Mixpanel"),
});
export type MixpanelCredentials = z.infer<typeof MixpanelCredentials>;

export const MixpanelCredentialsUi: Partial<Record<keyof MixpanelCredentials, PropertyUI>> = {
  serviceAccountPassword: {
    password: true,
  },
};

export const JuneCredentials = z.object({
  apiKey: z
    .string()
    .describe(
      `API Key::To get or create an API Key, go to workspace's "Settings & integrations" > Integrations > June SDK`
    ),
  enableAnonymousUserProfiles: z
    .boolean()
    .optional()
    .default(false)
    .describe("If enabled, anonymous users will be tracked in June"),
});

export const JuneCredentialsUi: Partial<Record<keyof JuneCredentials, PropertyUI>> = {
  apiKey: {
    password: true,
  },
};

export type JuneCredentials = z.infer<typeof JuneCredentials>;

export const BrazeCredentials = z.object({
  apiKey: z
    .string()
    .describe(
      "API Key::Created under Developer Console in the Braze Dashboard. Must have User Data and Events permissions."
    ),
  endpoint: z
    .enum([
      "US-01 : dashboard-01.braze.com",
      "US-02 : dashboard-02.braze.com",
      "US-03 : dashboard-03.braze.com",
      "US-04 : dashboard-04.braze.com",
      "US-05 : dashboard-05.braze.com",
      "US-06 : dashboard-06.braze.com",
      "US-07 : dashboard-07.braze.com",
      "US-08 : dashboard-08.braze.com",
      "US-09 : dashboard-09.braze.com",
      "EU-01 : dashboard-01.braze.eu",
      "EU-02 : dashboard-02.braze.eu",
    ])
    .optional()
    .default("US-01 : dashboard-01.braze.com")
    .describe(
      "Your Braze REST endpoint. <a target='_blank' rel='noopener noreferrer' href='https://www.braze.com/docs/api/basics/#endpoints'>See more details</a>"
    ),
  appId: z
    .string()
    .optional()
    .describe(
      "App ID::The app identifier used to reference specific Apps in requests made to the Braze API. Created under Developer Console in the Braze Dashboard."
    ),
  useJitsuAnonymousIdAlias: z
    .boolean()
    .optional()
    .default(false)
    .describe(
      "Use Anonymous Id Alias::Use Jitsu <code>anonymousId</code> as an alias for identified and anonymous profiles. Enables support for anonymous (alias-only) profiles."
    ),
  sendPageEvents: z
    .boolean()
    .optional()
    .default(false)
    .describe("Send <code>page</code> and <code>screen</code> events as Braze Custom Events"),
});

export const BrazeCredentialsUi: Partial<Record<keyof BrazeCredentials, PropertyUI>> = {
  apiKey: {
    password: true,
  },
};
export type BrazeCredentials = z.infer<typeof BrazeCredentials>;

export const BrazeDeviceCredentials = z.object({
  sdkVersion: z
    .enum(["4.1", "4.6", "4.8", "4.10", "5.4", "5.7"])
    .default("4.8")
    .describe("The version of the Braze SDK to use"),
  apiKey: z
    .string()
    .describe(
      "Note that this key is distinct from your Braze 'Classic' API key. Found in the Braze Dashboard under Manage Settings → Apps → Web. SDK API keys are not secret."
    ),
  endpoint: z
    .enum([
      "US-01 : dashboard-01.braze.com",
      "US-02 : dashboard-02.braze.com",
      "US-03 : dashboard-03.braze.com",
      "US-04 : dashboard-04.braze.com",
      "US-05 : dashboard-05.braze.com",
      "US-06 : dashboard-06.braze.com",
      "US-07 : dashboard-07.braze.com",
      "US-08 : dashboard-08.braze.com",
      "US-09 : dashboard-09.braze.com",
      "EU-01 : dashboard-01.braze.eu",
      "EU-02 : dashboard-02.braze.eu",
    ])
    .optional()
    .default("US-01 : dashboard-01.braze.com")
    .describe(
      "Your Braze SDK endpoint. <a target='_blank' rel='noopener noreferrer' href='https://www.braze.com/docs/user_guide/administrative/access_braze/sdk_endpoints'>See more details</a>"
    ),
  debug: z.boolean().describe("If enabled, the Braze SDK will log useful messages to the console."),
  enableOrderCompletedCalls: z
    .boolean()
    .default(true)
    .describe("Event type = 'track' and event = 'Order Completed'<br> Triggers Track Purchase"),
  enableTrackCalls: z
    .boolean()
    .default(true)
    .describe("Event type = 'track' and event != 'Order Completed' <br> Triggers Track Event"),
  enableIdentifyCalls: z
    .boolean()
    .default(true)
    .describe("Event type = 'identify'<br> Event type = 'group'<br> Triggers Update User Profile"),
});

export type BrazeDeviceCredentials = z.infer<typeof BrazeDeviceCredentials>;

export const SegmentCredentials = z.object({
  apiBase: z.string().default("https://api.segment.io/v1").describe("API Base::Segment API Base"),
  writeKey: z
    .string()
    .describe(
      `To get an API Key you need to add the HTTP API source to your Segment workspace. Write Key can be found on the HTTP API source Overview page.`
    ),
});

export const SegmentCredentialsUi: Partial<Record<keyof SegmentCredentials, PropertyUI>> = {
  writeKey: {
    password: true,
  },
};
export type SegmentCredentials = z.infer<typeof SegmentCredentials>;

export const POSTHOG_DEFAULT_HOST = "https://app.posthog.com";

export const PosthogDestinationConfig = z.object({
  key: z
    .string()
    .describe(
      "Project API Key::Posthog Project API Key. Can be found in <a target='_blank' rel='noopener noreferrer' href='https://app.posthog.com/project/settings'>Project Settings</a>"
    ),
  host: z.string().optional().default(POSTHOG_DEFAULT_HOST).describe("Posthog host"),
  enableGroupAnalytics: z
    .boolean()
    .optional()
    .default(false)
    .describe(
      "Group analytics is a powerful feature in PostHog that allows you to perform analytics on entities other than single users. Group Analytics is not available on the open-source or free cloud plan. <a href='https://posthog.com/pricing' target='_blank' rel='noreferrer noopener'>Learn more.</a>"
    ),
  groupType: z
    .string()
    .optional()
    .default("chord_group")
    .describe(
      "Group type is the abstract type of whatever our group represents (e.g. company, team, chat, post, etc.). <a href='https://posthog.com/docs/getting-started/group-analytics#groups-vs-group-types' target='_blank' rel='noreferrer noopener'>Groups vs. group types.</a>"
    ),
  sendAnonymousEvents: z
    .boolean()
    .optional()
    .default(false)
    .describe("Send events from anonymous users. If disabled, only events from identified users will be tracked."),
  enableAnonymousUserProfiles: z
    .boolean()
    .optional()
    .default(false)
    .describe(
      "Enable Anonymous Person Profiles::Create <a href='https://posthog.com/docs/getting-started/person-properties' target='_blank' rel='noreferrer noopener'>Person profiles</a> for anonymous users. If disabled, only identified users will have profiles."
    ),
});

export const PosthogDestinationConfigUi: Partial<Record<keyof PosthogDestinationConfig, PropertyUI>> = {
  sendAnonymousEvents: {
    // assumes value of this freshly added property from the value of the `enableAnonymousUserProfiles` property
    correction: obj =>
      typeof obj?.sendAnonymousEvents === "undefined" ? obj?.enableAnonymousUserProfiles : obj?.sendAnonymousEvents,
  },
  enableAnonymousUserProfiles: {
    hidden: obj =>
      typeof obj?.sendAnonymousEvents === "undefined" ? !obj.enableAnonymousUserProfiles : !obj.sendAnonymousEvents,
  },
  key: {
    password: true,
  },
};

export type PosthogDestinationConfig = z.infer<typeof PosthogDestinationConfig>;

export const AmplitudeDestinationConfig = z.object({
  key: z.string().describe("Project API Key::Amplitude Project API Key."),
  enableGroupAnalytics: z
    .boolean()
    .optional()
    .default(false)
    .describe(
      "Build an analysis around aggregated units of measure like accounts, charts, or order IDs. Requires The Amplitude Accounts add-on. <a href='https://help.amplitude.com/hc/en-us/articles/************-Account-level-reporting-in-Amplitude' target='_blank' rel='noreferrer noopener'>Learn more.</a>"
    ),
  groupType: z
    .string()
    .optional()
    .default("company")
    .describe(
      "Group type is the abstract type of whatever our group represents (e.g. accounts, charts, or order IDs)."
    ),
  enableAnonymousUserProfiles: z
    .boolean()
    .optional()
    .default(false)
    .describe("If enabled, anonymous users will be tracked in Amplitude"),
  dataResidency: z.enum(["US", "EU"]).optional().default("US"),
  sessionWindow: z.number().optional().default(30).describe("Session window in minutes"),
});
export const AmplitudeDestinationConfigUi: Partial<
  Record<keyof z.infer<typeof AmplitudeDestinationConfig>, PropertyUI>
> = {
  key: {
    password: true,
  },
};

export type AmplitudeDestinationConfig = z.infer<typeof AmplitudeDestinationConfig>;

export const MongodbDestinationConfig = z.object({
  url: z.string().optional(),
  protocol: z
    .enum(["mongodb", "mongodb+srv"])
    .default("mongodb")
    .describe(
      "MongoDB protocol. <code>mongodb</code> or <code>mongodb+srv</code>. For Atlas use <code>mongodb+srv</code>"
    ),
  hosts: z
    .array(z.string())
    .optional()
    .describe("MongoDB hosts with port (e.g. <code>localhost:27017</code>). One on each line"),
  username: z.string().describe("MongoDB username"),
  password: z.string().describe("MongoDB password"),
  database: z.string().describe("MongoDB database"),
  collection: z.string().describe("MongoDB collection"),

  options: z.object({}).catchall(z.string().default("")).optional().describe("Additional MongoDB connection options."),
});

export const MongodbDestinationConfigUi: Partial<Record<keyof MongodbDestinationConfig, PropertyUI>> = {
  hosts: {
    editor: "StringArrayEditor",
  },
  url: {
    hidden: true,
  },
  password: {
    password: true,
  },
};

export type MongodbDestinationConfig = z.infer<typeof MongodbDestinationConfig>;

export const Ga4Credentials = z.object({
  apiSecret: z
    .string()
    .describe(
      "An API SECRET generated in the Google Analytics UI, navigate to: Admin > Data Streams > choose your stream > Measurement Protocol > Create"
    ),
  measurementId: z
    .string()
    .describe(
      "The Measurement ID associated with a stream. Found in the Google Analytics UI under: Admin > Data Streams > choose your stream > Measurement ID. <b>Required for web streams.</b>"
    ),
  firebaseAppId: z
    .string()
    .optional()
    .describe(
      "The Firebase App ID associated with the Firebase app. Found in the Firebase console under: Project Settings > General > Your Apps > App ID. <b>Required for mobile app streams.</b>"
    ),
  url: z
    .string()
    .url()
    .describe(
      "Measurement Protocol URL.<br/>Default: <code>https://www.google-analytics.com/mp/collect</code><br/>Default debug url: <code>https://www.google-analytics.com/debug/mp/collect</code>"
    )
    .default("https://www.google-analytics.com/mp/collect"),
  events: z.string().optional().default("").describe(eventsParamDescription),
});

export const Ga4CredentialsUi: Partial<Record<keyof z.infer<typeof Ga4Credentials>, PropertyUI>> = {
  apiSecret: {
    password: true,
  },
};

export type Ga4Credentials = z.infer<typeof Ga4Credentials>;

export const HubspotCredentials = z.object({
  accessToken: z
    .string()
    .describe(
      [
        "To obtain an access secret, go to <b>Settings » Account Setup » Private Apps</b>, create a new private app copy the <b>Access token</b>. See <a href='https://developers.hubspot.com/docs/api/private-apps'>detailed guide</a>.",
        "Please make sure to grant an application all read and write permissions under CRM section",
      ].join("\n")
    ),
  sendPageViewEvents: z
    .boolean()
    .optional()
    .describe("When enabled, Jitsu will send page view events to hubspot (only events with a known email)"),
  autoCreateCustomProperties: z
    .boolean()
    .optional()
    .describe(
      "When enabled, Jitsu will automatically create HubSpot <a href='https://knowledge.hubspot.com/properties/create-and-edit-properties'>custom properties</a> for Contacts and Companies to capture every new trait. Otherwise, only known properties are sent."
    ),
});

export const HubspotCredentialsConfigUi: Partial<Record<keyof z.infer<typeof HubspotCredentials>, PropertyUI>> = {
  accessToken: {
    password: true,
  },
};

export type HubspotCredentials = z.infer<typeof HubspotCredentials>;

export const KlaviyoCredentials = z.object({
  apiKey: z
    .string()
    .describe("You can find this by going to Klaviyo's UI and clicking Account > Settings > API Keys > Create API Key"),
});

export const KlaviyoCredentialsConfigUi: Partial<Record<keyof z.infer<typeof KlaviyoCredentials>, PropertyUI>> = {
  apiKey: {
    password: true,
  },
};

export type KlaviyoCredentials = z.infer<typeof KlaviyoCredentials>;

export const AttentiveCredentials = z.object({
  apiKey: z
    .string()
    .describe(
      "A <a href='https://docs.attentive.com/pages/create-and-manage-custom-apps/'>custom app</a> is required to generate an API Key"
    ),
});

export const AttentiveCredentialsConfigUi: Partial<Record<keyof z.infer<typeof AttentiveCredentials>, PropertyUI>> = {
  apiKey: {
    password: true,
  },
};

export type AttentiveCredentials = z.infer<typeof AttentiveCredentials>;

export const TikTokConversionsDestinationConfig = z.object({
  accessToken: z
    .string()
    .describe(
      "Your TikTok Access Token. Please see TikTok’s <a href='https://ads.tiktok.com/marketing_api/docs?id=****************'>Events API documentation</a> for information on how to generate an access token via the TikTok Ads Manager or API."
    ),
  pixelCode: z
    .string()
    .describe(
      "Your TikTok Pixel ID. Please see TikTok’s <a href='https://ads.tiktok.com/marketing_api/docs?id=****************'>Events API documentation</a> for information on how to find this value."
    ),
  testEventCode: z
    .string()
    .optional()
    .describe("The Test Event Code can be found in the events manager of the TikTok ads platform."),
});

export const TikTokConversionsDestinationConfigUi: Partial<
  Record<keyof z.infer<typeof TikTokConversionsDestinationConfig>, PropertyUI>
> = {
  accessToken: {
    password: true,
  },
};

export type TikTokConversionsCredentials = z.infer<typeof TikTokConversionsDestinationConfig>;

export const PinterestTagCredentials = z.object({
  tagId: z.string().describe("Enter your Pinterest Tag ID. It should be a series of numbers, like 123456789."),
  useEnhancedMatchLoading: z
    .boolean()
    .optional()
    .describe(
      "If this setting is enabled, the Pinterest tag will be loaded with the existing traits of the logged user (if available). See the <a target='_blank' href='https://developers.pinterest.com/docs/overview/welcome/'>official documentation</a> for more information."
    )
    .default(false),
});

export type PinterestTagCredentials = z.infer<typeof PinterestTagCredentials>;

export const PinterestConversionsDestinationConfig = z.object({
  appName: z.string().describe("The name of the Site from which the event originated."),
  adAccountId: z
    .string()
    .describe(
      "Unique identifier of an ad account. This can be found in the Pinterest UI by following the steps mentioned <a href='https://developers.pinterest.com/docs/conversions/conversions/#Find%20your%20%2Cad_account_id'>here</a>."
    ),
  conversionToken: z
    .string()
    .describe(
      "The conversion token for your Pinterest account. This can be found in the Pinterest UI by following the steps mentioned <a href='https://developers.pinterest.com/docs/conversions/conversions/#Get%20the%20conversion%20token'>here</a>."
    ),
  testMode: z
    .boolean()
    .optional()
    .describe("Enable test mode, which shows all events in a debug tool on the Pinterest UI"),
});

export const PinterestConversionsDestinationConfigUi: Partial<
  Record<keyof z.infer<typeof PinterestConversionsDestinationConfig>, PropertyUI>
> = {
  conversionToken: {
    password: true,
  },
};

export type PinterestConversionsCredentials = z.infer<typeof PinterestConversionsDestinationConfig>;

export const SnapConversionsDestinationConfig = z.object({
  pixelId: z.string().describe("The Pixel ID for your Snapchat Ad Account. Required for web and offline events."),
  accessToken: z
    .string()
    .describe(
      "The generated Conversions API Token for your Snapchat Ads account found in the Business Details section."
    ),
  testMode: z
    .boolean()
    .optional()
    .describe(
      "Enable test mode, which appends '/validate' to the endpoint which shows all events in a debug tool on the Snapchat UI"
    ),
});

export const SnapConversionsDestinationConfigUi: Partial<
  Record<keyof z.infer<typeof SnapConversionsDestinationConfig>, PropertyUI>
> = {
  accessToken: {
    password: true,
  },
};

export type SnapConversionsCredentials = z.infer<typeof SnapConversionsDestinationConfig>;

export const TikTokPixelCredentials = z.object({
  pixelCode: z
    .string()
    .describe(
      "Your TikTok Pixel ID. Please see TikTok's Pixel <a href='https://ads.tiktok.com/marketing_api/docs?id=****************' target='_blank'>documentation</a> for information on how to find this value."
    ),
  ldu: z
    .boolean()
    .optional()
    .describe(
      'In order to help facilitate advertiser\'s compliance with the right to opt-out of sale and sharing of personal data under certain U.S. state privacy laws, TikTok offers a Limited Data Use ("LDU") feature. For more information, please refer to TikTok\'s <a href="https://business-api.tiktok.com/portal/docs?id=1770092377990145" target="_blank">documentation page</a>.'
    ),
  autoPageView: z
    .boolean()
    .optional()
    .default(true)
    .describe('If true, TikTok Pixel will fire a "Pageview" event when the pixel is loaded on the page.'),
});

export type TikTokPixelCredentials = z.infer<typeof TikTokPixelCredentials>;

export const BingAdsCredentials = z.object({
  tagId: z
    .string()
    .describe("Your Bing Universal Event Tracking Tag ID. The ID can be found in your Microsoft Ads dashboard."),
});

export type BingAdsCredentials = z.infer<typeof BingAdsCredentials>;

export const NinetailedCredentials = z.object({
  sendPageEvents: z
    .boolean()
    .optional()
    .default(true)
    .describe("If enabled, all page view events will be sent to Ninetailed."),
});

export type NinetailedCredentials = z.infer<typeof NinetailedCredentials>;

export const NinetailedCloudDestinationConfig = z.object({
  organizationId: z
    .string()
    .describe("The ID of the organization in Ninetailed. This can be found in your Ninetailed dashboard."),
  environmentSlug: z
    .string()
    .describe("The slug of the environment in Ninetailed. This can be found in your Ninetailed dashboard.")
    .default("main"),
});

export type NinetailedCloudCredentials = z.infer<typeof NinetailedCloudDestinationConfig>;

export const ImpactCredentials = z.object({
  campaignId: z.string().optional().describe("Impact Campaign ID"),
  accountSid: z.string().optional().describe("Account SID for authentication"),
  apiKey: z.string().optional().describe("API Key for authentication"),
  eventTypeId: z.string().optional().describe("Custom event type ID"),
  enablePageEvents: z.boolean().optional().describe("Whether to track page view events"),
  enableIdentifyEvents: z.boolean().optional().describe("Whether to track identify events"),
  customParameterMapping: z.record(z.string(), z.string()).optional().describe("Custom field mappings"),
  customMappingForProducts: z.record(z.string(), z.string()).optional().describe("Custom product field mappings"),
  actionEventNames: z.array(z.string()).optional().describe("Custom event names for conversions"),
  pageLoadEventNames: z.array(z.string()).optional().describe("Custom event names for page views"),
});

export const ImpactCredentialsUi: Partial<Record<keyof z.infer<typeof ImpactCredentials>, PropertyUI>> = {
  apiKey: {
    password: true,
  },
};
