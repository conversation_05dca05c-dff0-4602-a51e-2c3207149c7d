import { testJitsuFunction, TestOptions } from "./lib/testing-lib";
import PinterestConversionsDestination from "../src/functions/pinterest-conversions-destination";
import { PinterestConversionsCredentials } from "../src/meta";
import { eventsSequence } from "./lib/test-data-chord";

// npx pnpm tsc -p . && CONVERSION_TOKEN="" AD_ACCOUNT_ID="************" APP_NAME="Chord Test" npx pnpm jest --verbose -t 'pinterest-converions-destination-integration'
test("pinterest-converions-destination-integration", async () => {
  if (!process.env.AD_ACCOUNT_ID && !process.env.CONVERSION_TOKEN && !process.env.APP_NAME) {
    console.log(
      "Skipping Pinterest destination integration test - or AD_ACCOUNT_ID or CONVERSION_TOKEN or APP_NAME is not set"
    );
    return;
  }

  const opts: TestOptions<PinterestConversionsCredentials> = {
    func: PinterestConversionsDestination,
    config: {
      adAccountId: process.env.AD_ACCOUNT_ID || "",
      conversionToken: process.env.CONVERSION_TOKEN || "",
      testMode: true,
      appName: process.env.APP_NAME || "",
    },
    events: eventsSequence(),
  };
  await testJitsuFunction(opts);
});
