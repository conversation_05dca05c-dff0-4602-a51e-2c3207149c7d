import { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { randomId } from "juava";

function createContext(url: string, userTraits: Record<string, any> = {}): any {
  const parsedUrl = new URL(url);
  return {
    app: {},
    campaign: {
      name: "name",
      source: "source",
    },
    ip: "*************",
    library: {
      name: "Jitsu",
      version: "1.0",
    },
    locale: "en-US",
    os: {
      name: "",
      version: "",
    },
    page: {
      path: parsedUrl.pathname,
      referrer: "$direct",
      search: parsedUrl.search,
      title: `Title: ${parsedUrl.pathname}`,
      url: url,
    },
    traits: userTraits || {},
    screen: {
      density: 2,
      height: 2000,
      innerHeight: 1000,
      innerWidth: 2000,
      width: 1000,
    },
    userAgent:
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  };
}

function event(
  type: string,
  opts: {
    anonymousId?: string;
    url: string;
    userId?: string;
    traits?: Record<string, any>;
    properties?: {};
  }
): AnalyticsServerEvent {
  const [eventType, eventSubtype] = type.indexOf("/") > 0 ? type.split("/") : [type, undefined];
  return {
    type: eventType as any,
    event: eventSubtype,
    ...(opts.anonymousId ? { anonymousId: opts.anonymousId } : {}),
    ...(opts.userId ? { userId: opts.userId } : {}),
    channel: "web",
    context: createContext(opts.url, opts.traits),
    messageId: randomId(),
    originalTimestamp: new Date().toISOString(),
    properties: opts.properties || {},
    receivedAt: new Date().toISOString(),
    requestIp: "*************",
    sentAt: new Date().toISOString(),
    timestamp: new Date().toISOString(),
    traits: opts.traits || {},
  };
}

function identify(opts: {
  anonymousId?: string;
  url: string;
  userId?: string;
  traits?: Record<string, any>;
  groupId?: string;
}): AnalyticsServerEvent {
  return {
    type: "identify",
    ...(opts.anonymousId ? { anonymousId: opts.anonymousId } : {}),
    ...(opts.userId ? { userId: opts.userId } : {}),
    channel: "web",
    traits: opts.traits || {},
    context: createContext(opts.url),
    messageId: randomId(),
    originalTimestamp: new Date().toISOString(),
    receivedAt: new Date().toISOString(),
    requestIp: "*************",
    sentAt: new Date().toISOString(),
    timestamp: new Date().toISOString(),
    groupId: opts.groupId,
  };
}

export function eventsSequence(
  config = { coreEvents: true, basicEvents: true, identifyEvents: true, customEvents: true }
): AnalyticsServerEvent[] {
  const ids = {
    anonymousId: randomId(),
    userId: randomId(),
  };

  const product = {
    name: "Chord Mock Product",
    price: 54,
    product_id: "chord-mock-product-id",
    quantity: 1,
    sku: "chord-mock-product-sku",
    variant: "chord-mock-product-variant",
  };

  const productTwo = {
    name: "Chord Mock Product Two",
    price: 45,
    product_id: "chord-mock-product-id-two",
    quantity: 2,
    sku: "chord-mock-product-sku-two",
    variant: "chord-mock-product-variant-two",
  };

  const meta = {
    i18n: { locale: "en-US", currency: "USD" },
    ownership: [
      {
        tenant_id: randomId(),
        oms_id: randomId(),
        store_id: randomId(),
      },
    ],
  };

  const traits = {
    email: `gus+${randomId()}@chord.co`,
    phone: "2039154459",
  };

  const url = "https://www.chordcommerce.com/";

  const identifyEvent = identify({ ...ids, traits, url });
  const identifyEventWithAddress = identify({
    ...ids,
    traits: {
      ...traits,
      name: "John Doe",
      address: {
        street: "12345 Street Rd",
        street2: "Unit A",
        city: "Kirkwood",
        state: "PA",
        postalCode: "17536",
        country: "US",
      },
    },
    url,
  });

  const productAddedEvent = event("track/Product Added", {
    ...ids,
    properties: {
      ...product,
      meta,
    },
    traits,
    url,
  });

  const cartViewedEvent = event("track/Cart Viewed", {
    ...ids,
    properties: {
      cart_id: "CHORD-470061239",
      products: [product],
      meta,
    },
    traits,
    url,
  });

  const checkoutStartedEvent = event("track/Checkout Started", {
    ...ids,
    properties: {
      order_id: "CHORD-470061239",
      products: [product],
      meta,
    },
    traits,
    url,
  });

  const orderCompletedEvent = event("track/Order Completed", {
    ...ids,
    properties: {
      currency: "USD",
      email: traits.email,
      order_id: "CHORD-470061239",
      revenue: 61.99,
      total: 61.99,
      products: [product],
      meta,
    },
    traits,
    url,
  });

  const productRemovedEvent = event("track/Product Removed", {
    ...ids,
    properties: {
      ...product,
      meta,
    },
    traits,
    url,
  });

  const productClickedEvent = event("track/Product Clicked", {
    ...ids,
    properties: {
      ...product,
      meta,
    },
    traits,
    url,
  });

  const productViewedEvent = event("track/Product Viewed", {
    ...ids,
    properties: {
      ...product,
      meta,
    },
    traits,
    url,
  });

  const productListViewedEvent = event("track/Product List Viewed", {
    ...ids,
    properties: {
      products: [product, productTwo],
      meta,
    },
    traits,
    url,
  });

  const customSizeGuideViewedEvent = event("track/Size Guide Viewed", {
    ...ids,
    properties: {
      ...product,
      meta,
    },
    traits,
    url,
  });

  const customSearchRefinementEvent = event("track/Search Refinement", {
    ...ids,
    properties: {
      initial_query: "sneakers",
      refined_query: "white running sneakers",
      refinement_type: "keyword change",
      ...meta,
    },
    traits,
    url,
  });

  return [
    ...(config.identifyEvents ? [identifyEvent] : []),
    ...(config.coreEvents ? [productAddedEvent] : []),
    ...(config.coreEvents ? [cartViewedEvent] : []),
    ...(config.coreEvents ? [checkoutStartedEvent] : []),
    ...(config.identifyEvents ? [identifyEventWithAddress] : []),
    ...(config.coreEvents ? [orderCompletedEvent] : []),
    ...(config.basicEvents ? [productRemovedEvent] : []),
    ...(config.basicEvents ? [productClickedEvent] : []),
    ...(config.basicEvents ? [productViewedEvent] : []),
    ...(config.basicEvents ? [productListViewedEvent] : []),
    ...(config.customEvents ? [customSizeGuideViewedEvent] : []),
    ...(config.customEvents ? [customSearchRefinementEvent] : []),
  ];
}
