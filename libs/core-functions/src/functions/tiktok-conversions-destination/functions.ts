import type { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { FullContext } from "@jitsu/protocols/functions";
import { formatEmails, formatPhones, formatUserIds, formatString, formatAddress } from "./formatter";
import type {
  TikTokConversionsPage,
  TikTokConversionsProperties,
  TikTokConversionsRequest,
  TikTokConversionsUser,
} from "./types";
import { eventPresetMappings, singleProductEvents, multiProductEvents } from "./config";

/**
 * Build headers for TikTok Conversions API requests
 */
export function buildHeaders(accessToken: string): Record<string, string> {
  return {
    "Content-Type": "application/json",
    "Access-Token": accessToken,
  };
}

/**
 * Convert an analytics event to TikTok Conversions API format
 */
export function createEventData(event: AnalyticsServerEvent, ctx: FullContext): TikTokConversionsRequest {
  try {
    const props = ctx.props;
    const eventName = event.event ?? "Pageview";
    const timestamp = getUnixTimestamp(event.timestamp);
    const mappedEventName = eventPresetMappings[eventName as keyof typeof eventPresetMappings] || "Pageview";

    return {
      event_source: "web",
      event_source_id: props.pixelCode,
      ...(props.testEventCode?.length ? { test_event_code: props.testEventCode } : {}),
      data: [
        {
          event: mappedEventName,
          event_time: timestamp,
          event_id: event.messageId ? String(event.messageId) : undefined,
          user: extractUserData(event),
          properties: extractPropertiesData(event),
          page: extractPageData(event),
          limited_data_use: !!event.limited_data_use,
        },
      ],
    };
  } catch (error: any) {
    throw new Error(`Failed to create TikTok event data: ${error.message}`);
  }
}

/**
 * Convert a timestamp to Unix timestamp (seconds)
 */
function getUnixTimestamp(timestamp?: string | number): number {
  return timestamp ? Math.floor(new Date(timestamp).getTime() / 1000) : Math.floor(Date.now() / 1000);
}

/**
 * Extract and format user data from the event
 */
function extractUserData(payload: AnalyticsServerEvent): TikTokConversionsUser {
  const properties = payload.properties || {};
  const context = payload.context || {};
  const traits = context.traits || {};
  const addressSource = properties.address || traits.address || payload.address || {};

  // Extract identification data
  const phoneSource = properties.phone || traits.phone;
  const emailSource = properties.email || traits.email;
  const userId = (payload.userId ?? payload.anonymousId ?? "").toString();

  // Format user identifiers
  const user: TikTokConversionsUser = {
    external_id: formatUserIds([userId]),
    phone: formatPhones(phoneSource ? [String(phoneSource)] : undefined),
    email: formatEmails(emailSource ? [String(emailSource)] : undefined),
    first_name: formatString(properties.first_name || traits.first_name || payload.first_name),
    last_name: formatString(properties.last_name || traits.last_name || payload.last_name),
    city: formatAddress(addressSource.city),
    state: formatAddress(addressSource.state),
    country: formatAddress(addressSource.country),
    zip_code: formatString(addressSource.postal_code || addressSource.zip_code),
  };

  // Extract TikTok-specific identifiers
  addTikTokIdentifiers(user, payload);

  // Add additional context data
  if (context.ip) user.ip = context.ip;
  if (context.user_agent || context.userAgent) {
    user.user_agent = context.user_agent || context.userAgent;
  }
  if (context.locale) user.locale = context.locale;

  return user;
}

/**
 * Add TikTok-specific identifiers to user data
 */
function addTikTokIdentifiers(user: TikTokConversionsUser, payload: AnalyticsServerEvent): void {
  // Extract ttclid from URL if present
  let ttclidFromUrl: string | null = null;
  const url = payload.page?.url || payload.context?.page?.url;

  if (url) {
    try {
      const payloadUrl = new URL(url);
      ttclidFromUrl = payloadUrl.searchParams.get("ttclid");
    } catch {
      // Invalid URL - ignore
    }
  }

  const properties = payload.properties || {};
  const tikTokIntegration = payload.integrations?.["TikTok Conversions"] || {};

  // Get TikTok identifiers from various sources
  const ttclid = properties.ttclid || tikTokIntegration.ttclid || ttclidFromUrl || payload.ttclid;
  const ttp = properties.ttp || tikTokIntegration.ttp || payload.ttp;
  const lead_id = properties.lead_id || payload.lead_id;

  // Add identifiers if they exist
  if (ttclid) user.ttclid = ttclid;
  if (ttp) user.ttp = ttp;
  if (lead_id) user.lead_id = lead_id;
}

/**
 * Extract and format properties data from the event
 */
function extractPropertiesData(payload: AnalyticsServerEvent): TikTokConversionsProperties {
  const properties: TikTokConversionsProperties = {
    contents: [],
  } as TikTokConversionsProperties;

  const prop = payload.properties || {};
  const eventName = payload.event || payload.type || "";

  // Process any existing contents array
  if (Array.isArray(payload.contents)) {
    properties.contents = payload.contents.map(content => ({
      price: content.properties?.price,
      quantity: content.properties?.quantity,
      content_category: content.content_category,
      content_id: content.properties?.product_id,
      content_name: content.properties?.name,
      brand: content.properties?.brand,
    }));
  }

  // Process by event type
  processEventTypeSpecificData(properties, eventName, prop, payload);

  // Handle currency
  setCurrency(properties, prop);

  // Calculate value if not explicitly provided
  calculateValue(properties, prop);

  // Add additional fields
  addAdditionalProperties(properties, prop, eventName);

  return properties;
}

/**
 * Process event type specific data
 */
function processEventTypeSpecificData(
  properties: TikTokConversionsProperties,
  eventName: string,
  prop: Record<string, any>,
  payload: AnalyticsServerEvent
): void {
  // Handle single product events
  if (singleProductEvents.includes(eventName)) {
    addSingleProductContent(properties, prop);
  }

  // Handle multi-product events
  if (multiProductEvents.includes(eventName) || (eventName === "page" && payload.page)) {
    addMultiProductContents(properties, prop);
  }
}

/**
 * Add a single product content item
 */
function addSingleProductContent(properties: TikTokConversionsProperties, prop: Record<string, any>): void {
  const contentItem: Record<string, any> = {};

  if (prop.price !== undefined) contentItem.price = Number(prop.price);
  if (prop.quantity !== undefined) contentItem.quantity = Number(prop.quantity);
  if (prop.category !== undefined) contentItem.content_category = String(prop.category);
  if (prop.product_id !== undefined) contentItem.content_id = String(prop.product_id);
  if (prop.name !== undefined) contentItem.content_name = String(prop.name);
  if (prop.brand !== undefined) contentItem.brand = String(prop.brand);

  if (Object.keys(contentItem).length > 0) {
    properties.contents.push(contentItem);
  }
}

/**
 * Add multiple product content items
 */
function addMultiProductContents(properties: TikTokConversionsProperties, prop: Record<string, any>): void {
  if (!Array.isArray(prop.products)) return;

  for (const product of prop.products) {
    if (typeof product !== "object" || product === null) continue;

    const contentItem: Record<string, any> = {};
    const fields = [
      { key: "price", type: "number" },
      { key: "quantity", type: "number" },
      { key: "category", target: "content_category", type: "string" },
      { key: "product_id", target: "content_id", type: "string" },
      { key: "name", target: "content_name", type: "string" },
      { key: "brand", type: "string" },
    ];

    for (const field of fields) {
      if (!(field.key in product) || product[field.key] === undefined) continue;

      const targetKey = field.target || field.key;
      contentItem[targetKey] = field.type === "number" ? Number(product[field.key]) : String(product[field.key]);
    }

    if (Object.keys(contentItem).length > 0) {
      properties.contents.push(contentItem);
    }
  }
}

/**
 * Set currency from available sources
 */
function setCurrency(properties: TikTokConversionsProperties, prop: Record<string, any>): void {
  if (prop.currency) {
    properties.currency = String(prop.currency);
    return;
  }

  if (typeof prop.meta !== "object" || prop.meta === null || !("i18n" in prop.meta)) return;

  if (typeof prop.meta.i18n === "object" && prop.meta.i18n !== null && "currency" in prop.meta.i18n) {
    properties.currency = String(prop.meta.i18n.currency);
  }
}

/**
 * Calculate value from available sources
 */
function calculateValue(properties: TikTokConversionsProperties, prop: Record<string, any>): void {
  if (prop.value !== undefined) {
    properties.value = Number(prop.value);
  } else if (prop.revenue !== undefined) {
    properties.value = Number(prop.revenue);
  } else if (properties.contents.length > 0) {
    // Calculate value from contents if available
    let calculatedValue = 0;
    let hasValidData = false;

    for (const item of properties.contents) {
      if (item.price !== undefined) {
        const quantity = item.quantity !== undefined ? Number(item.quantity) : 1;
        calculatedValue += Number(item.price) * quantity;
        hasValidData = true;
      }
    }

    if (hasValidData) {
      properties.value = calculatedValue;
    }
  }
}

/**
 * Add additional properties based on the event type
 */
function addAdditionalProperties(
  properties: TikTokConversionsProperties,
  prop: Record<string, any>,
  eventName: string
): void {
  // Add order-related fields
  if (prop.order_id) properties.order_id = String(prop.order_id);
  if (prop.shop_id) properties.shop_id = String(prop.shop_id);

  // Handle form events
  if (eventName === "Form Submitted") {
    if (prop.form_name) (properties as any).form_name = String(prop.form_name);
    else if (prop.name) (properties as any).form_name = String(prop.name);
  }

  // Handle search events
  if (eventName === "Products Searched") {
    if (prop.query) properties.query = String(prop.query);
    else if (prop.search_term) properties.query = String(prop.search_term);
  }

  // Add content_type if specified
  if (prop.content_type) {
    properties.content_type = prop.content_type === "product_group" ? "product_group" : "product";
  }

  // Add description if available
  if (prop.description) {
    properties.description = String(prop.description);
  }
}

/**
 * Extract and format page data from the event
 */
function extractPageData(payload: AnalyticsServerEvent): TikTokConversionsPage {
  const page: TikTokConversionsPage = {};

  // Extract URL from available sources
  const url = payload.page?.url || payload.context?.page?.url;
  if (url) page.url = url;

  // Extract referrer from available sources
  const referrer = payload.page?.referrer || payload.context?.page?.referrer;
  if (referrer) page.referrer = referrer;

  return page;
}
