---
openapi: 3.0.3
info:
  title: API for Chord CDP Console service
  description: API for interacting with Chord's CDP Console
  license:
    name: Proprietary
    url: https://www.chordcommerce.com/terms
  version: "1.0"
servers:
  - url: https://staging.cdp.chord.co
    description: Staging Console
  - url: https://production.cdp.chord.co
    description: Production Console
security:
  - BearerAuth: []
paths:
  /healthcheck:
    get:
      operationId: getHealth
      summary: Health check
      responses:
        "200":
          $ref: "#/components/responses/ResponseHealthCheckSuccess"
        "403":
          description: Unauthorized
  /api/{workspace_id}/config/stream:
    get:
      operationId: getWorkspaceStreams
      summary: Details regarding the specified workspace
      parameters:
        - $ref: "#/components/parameters/WorkspaceId"
      responses:
        "200":
          description: OK
        "403":
          description: Unauthorized
  /api/{workspace_id}/config/service:
    get:
      operationId: getWorkspaceServices
      summary: List services in the workspace
      parameters:
        - $ref: "#/components/parameters/WorkspaceId"
      responses:
        "200":
          description: OK
        "403":
          description: Unauthorized
  /api/{workspace_id}/config/destination:
    get:
      operationId: getWorkspaceDestinations
      summary: List destinations in the workspace
      parameters:
        - $ref: "#/components/parameters/WorkspaceId"
      responses:
        "200":
          description: OK
        "403":
          description: Unauthorized
    post:
      operationId: createWorkspaceDestination
      summary: Add a destination to the workspace
      parameters:
        - $ref: "#/components/parameters/WorkspaceId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Destination"
      responses:
        "200":
          description: OK
        "403":
          description: Unauthorized
  /api/{workspace_id}/config/destination/test:
    post:
      operationId: createBulkerDestinationTest
      summary: Test a Bulker destination
      parameters:
        - $ref: "#/components/parameters/WorkspaceId"
      responses:
        "200":
          description: OK
        "403":
          description: Unauthorized
  /api/{workspace_id}/config/destination/{destination_id}:
    put:
      operationId: updateWorkspaceDestination
      summary: Update a destintaion
      parameters:
        - $ref: "#/components/parameters/WorkspaceId"
        - $ref: "#/components/parameters/DestinationId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Destination"
      responses:
        "200":
          description: OK
        "403":
          description: Unauthorized
    delete:
      operationId: deleteWorkspaceDestination
      summary: Delete a destination from the workspace
      parameters:
        - $ref: "#/components/parameters/WorkspaceId"
        - $ref: "#/components/parameters/DestinationId"
      responses:
        "200":
          description: OK
        "403":
          description: Unauthorized
  /api/{workspace_id}/config/link:
    get:
      operationId: getWorkspaceLinks
      summary: List the links between sources and destinations in the workspace
      parameters:
        - $ref: "#/components/parameters/WorkspaceId"
      responses:
        "200":
          description: OK
        "403":
          description: Unauthorized
    post:
      operationId: createWorkspaceLink
      summary: Create a link between a source and a destination
      parameters:
        - $ref: "#/components/parameters/WorkspaceId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateLink"
      responses:
        "200":
          description: OK
        "403":
          description: Unauthorized
    delete:
      operationId: deleteWorkspaceLink
      summary: Delete a link between a source and a destination
      parameters:
        - $ref: "#/components/parameters/WorkspaceId"
        - $ref: "#/components/parameters/LinkId"
      responses:
        "200":
          description: OK
        "403":
          description: Unauthorized
  /api/{workspace_id}/log/incoming/{source_id}:
    get:
      operationId: getWorkspaceSourceLog
      summary: View log (Live Events) for source
      parameters:
        - $ref: "#/components/parameters/WorkspaceId"
        - $ref: "#/components/parameters/SourceId"
      responses:
        "200":
          description: OK
        "403":
          description: Unauthorized
  /api/{workspace_id}/log/{bulker_mode}/{destination_id}:
    get:
      operationId: getWorkspaceBulkerDestinationLog
      summary: View log for events passing through Bulker to destination
      parameters:
        - $ref: "#/components/parameters/WorkspaceId"
        - $ref: "#/components/parameters/BulkerMode"
        - $ref: "#/components/parameters/DestinationId"
      responses:
        "200":
          description: OK
        "403":
          description: Unauthorized
  /api/app-config:
    get:
      operationId: getAppConfig
      summary: Get general application configuration
      responses:
        "200":
          description: OK
        "403":
          description: Unauthorized
  /api/init-user:
    get:
      operationId: getUserInformation
      summary: Retrive the user information (based on API key)
      responses:
        "200":
          description: OK
        "403":
          description: Unauthorized
components:
  parameters:
    BulkerMode:
      name: bulker_mode
      in: path
      required: true
      description: bulker mode
      schema:
        type: string
    DestinationId:
      name: destination_id
      in: path
      required: true
      description: The destination ID
      schema:
        type: string
    LinkId:
      name: link_id
      in: query
      required: true
      description: link ID
      schema:
        type: string
    SourceId:
      name: source_id
      in: path
      required: true
      description: source ID
      schema:
        type: string
    WorkspaceId:
      name: workspace_id
      in: path
      required: true
      description: The workspace ID
      schema:
        type: string
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
  schemas:
    CreateLink:
      type: object
      properties:
        fromId:
          type: string
        toId:
          type: string
    Destination:
      type: object
      properties:
        destinationType:
          type: string
        id:
          type: string
        name:
          type: string
        type:
          type: string
        workspaceId:
          type: string
    ResponseHealthSuccess:
      type: object
      properties:
        status:
          type: string
          example: pass
  responses:
    ResponseHealthCheckSuccess:
      description: Health check successful
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ResponseHealthSuccess"
