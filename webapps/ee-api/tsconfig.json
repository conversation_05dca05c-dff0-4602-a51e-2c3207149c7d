{"extends": "../../tsconfig.base.json", "compilerOptions": {"target": "esnext", "module": "commonjs", "lib": ["dom", "dom.iterable", "esnext", "ES6", "ES2015"], "rootDir": ".", "allowJs": true, "skipLibCheck": true, "strict": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}