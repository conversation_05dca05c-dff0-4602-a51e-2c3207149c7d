import type { AnalyticsServerEvent, J<PERSON>NObject } from "@jitsu/protocols/analytics";
import type { AttentiveEventData, AttentiveUser } from "./types";
import { processPhoneNumber } from "../../utils";
import type { AnalyticsEventAddress } from "../../utils/types";
import { ErrorWithCode } from "../../lib/error-types";

export const ATTENTIVE_MISSING_IDENTIFIER = "klaviyoMissingIdentifier";

export function buildHeaders(authKey: string) {
  return {
    "Content-Type": "application/json",
    Authorization: `Bearer ${authKey}`,
  };
}

export function createEventData(event: AnalyticsServerEvent): AttentiveEventData {
  const address = (event?.context?.traits?.address || event?.traits?.address) as AnalyticsEventAddress;

  let phone = (event?.properties?.phone || event?.context?.traits?.phone || event?.traits?.phone) as string;

  if (phone) {
    phone = processPhoneNumber(phone, address?.country || "US");
  }

  const email = (event?.properties?.email || event?.context?.traits?.email || event?.traits?.email) as string;

  if (!phone && !email && !event?.userId && !event?.anonymousId) {
    throw new ErrorWithCode(
      "Either phone number, email, userId, or anonymousId is required.",
      ATTENTIVE_MISSING_IDENTIFIER
    );
  }

  return {
    type: event.event as string,
    properties: event?.properties as JSONObject,
    externalEventId: event?.messageId as string,
    // Timestamp of when the action occurred in ISO 8601 format.
    occurredAt: event?.timestamp as string,
    user: {
      // Phone number of the user associated with the action. E.164 format is required.
      phone: phone,
      email: email,
      externalIdentifiers: {
        clientUserId: event?.userId || (event?.anonymousId as string),
      },
    } as AttentiveUser,
  };
}
