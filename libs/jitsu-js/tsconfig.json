{
  "compilerOptions": {
    "rootDir": ".",
    "outDir": "./compiled",
    "declaration": true,
    "esModuleInterop": true,
    "moduleResolution": "Node",
    "resolveJsonModule": true,
    "target": "ES2015",
    "lib": ["es2020", "dom"],
    //this makes typescript igore @types/node during compilation
    "types": []
  },
  "include": ["./src", "./__tests__"],
  "exclude": [
    "__tests__",
    "node_modules",
    "dist",
    "test_projects",
    "test",
    "templates"
  ]
}