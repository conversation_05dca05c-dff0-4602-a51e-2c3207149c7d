{"name": "@jitsu-internal/webapps-shared", "version": "0.0.0", "private": true, "main": "src/index.ts", "types": "src/index.ts", "scripts": {}, "dependencies": {"dayjs": "^1.11.10", "nodemailer": "^6.10.1", "@react-email/components": "^0.0.34", "@react-email/render": "^1.0.5", "@types/node": "^18.15.3", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "juava": "workspace:*", "lodash": "^4.17.21", "react": "^18.3.1", "react-dom": "18.3.1", "tslib": "^2.6.3", "zod": "^3.23.8"}, "devDependencies": {"eslint": "^8.57.0", "@types/lodash": "^4.14.185", "ts-node": "^10.9.2", "type-fest": "^3.5.7", "typescript": "^5.6.3", "@types/nodemailer": "^6.4.17"}}