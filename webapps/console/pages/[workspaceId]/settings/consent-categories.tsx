import { WorkspacePageLayout } from "../../../components/PageLayout/WorkspacePageLayout";
import { ConfigEditor, ConfigEditorProps } from "../../../components/ConfigObjectEditor/ConfigEditor";
import { useWorkspace } from "../../../lib/context";
import React from "react";
import { ConsentCategoryConfig } from "../../../lib/schema";
import { CookieIcon } from "lucide-react";

const ConsentCategoriesPage: React.FC<any> = () => {
  return (
    <WorkspacePageLayout>
      <ConsentCategoryList />
    </WorkspacePageLayout>
  );
};

const ConsentCategoryList: React.FC<{}> = () => {
  const workspace = useWorkspace();

  const config: ConfigEditorProps<ConsentCategoryConfig> = {
    listColumns: [
      {
        title: "External ID",
        render: (category: any) => <span>{category.external_id}</span>,
      },
      {
        title: "Status",
        render: (category: any) => (
          <span className={category.active ? "text-green-600" : "text-gray-400"}>
            {category.active ? "Active" : "Inactive"}
          </span>
        ),
      },
    ],
    pathPrefix: "/settings",
    objectType: ConsentCategoryConfig,
    fields: {
      type: { constant: "consent-category" },
      workspaceId: { constant: workspace.id },
      name: {},
      external_id: {},
      description: {
        editor: "textarea",
      },
      active: {
        displayName: "Active",
        editor: "checkbox",
      },
    },
    noun: "Consent Category",
    nounPlural: "Consent Categories",
    type: "consent-category",
    typePlural: "consent-categories",
    listTitle: "Consent Categories",
    backTo: "consent-categories",
    explanation: "Manage consent categories for user privacy preferences",
    icon: () => <CookieIcon className="w-full h-full" />,
    editorTitle: (_: any, isNew: boolean) => {
      const verb = isNew ? "New" : "Edit";
      return (
        <div className="flex items-center">
          <div className="h-12 w-12 mr-4">
            <CookieIcon className="w-full h-full" />
          </div>
          {verb} Consent Category
        </div>
      );
    },
  };
  return (
    <>
      <ConfigEditor {...(config as any)} />
    </>
  );
};

export default ConsentCategoriesPage;
