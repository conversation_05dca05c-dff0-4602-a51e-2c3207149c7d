name: <PERSON>uild and Push Console and Rotor Images
run-name: Build dev${{ inputs.push_staging == true && ', staging' || '' }} ${{ inputs.push_production == true && ', prod' || '' }} images ${{ github.sha }}

on:
  workflow_dispatch:
    inputs:
      push_staging:
        description: "Push to staging"
        required: false
        type: boolean
      push_production:
        description: "Push to production"
        required: false
        type: boolean

permissions:
  id-token: write
  contents: read

jobs:
  build:
    name: Build console and rotor images
    timeout-minutes: 60
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Cache Docker layers
        uses: actions/cache@v4
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Build console image
        run: |
          docker buildx build \
          --platform linux/amd64 \
          --target console \
          --tag console:${{ github.sha }} \
          -f all.Dockerfile . \
          --load \
          --cache-to type=local,dest=/tmp/.buildx-cache

      - name: Build rotor image
        run: |
          docker buildx build \
          --platform linux/amd64 \
          --target rotor \
          --tag rotor:${{ github.sha }} \
          -f all.Dockerfile . \
          --load \
          --cache-from type=local,src=/tmp/.buildx-cache \
          --cache-to type=local,dest=/tmp/.buildx-cache

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::501922849989:role/ECRBuildAndPushRole
          aws-region: us-east-1
          role-session-name: Github_to_AWS_via_FederatedOIDC

      - name: Sts GetCallerIdentity
        run: aws sts get-caller-identity

      - name: Login to Amazon ECR
        id: login-ecr-development
        uses: aws-actions/amazon-ecr-login@v2

      - name: Push console image to development
        run: |
          docker tag console:${{ github.sha }} 501922849989.dkr.ecr.us-east-1.amazonaws.com/cdp/development/console:${{ github.sha }}
          docker tag console:${{ github.sha }} 501922849989.dkr.ecr.us-east-1.amazonaws.com/cdp/development/console:latest
          docker push 501922849989.dkr.ecr.us-east-1.amazonaws.com/cdp/development/console:${{ github.sha }}
          docker push 501922849989.dkr.ecr.us-east-1.amazonaws.com/cdp/development/console:latest

      - name: Push rotor image to development
        run: |
          docker tag rotor:${{ github.sha }} 501922849989.dkr.ecr.us-east-1.amazonaws.com/cdp/development/rotor:${{ github.sha }}
          docker tag rotor:${{ github.sha }} 501922849989.dkr.ecr.us-east-1.amazonaws.com/cdp/development/rotor:latest
          docker push 501922849989.dkr.ecr.us-east-1.amazonaws.com/cdp/development/rotor:${{ github.sha }}
          docker push 501922849989.dkr.ecr.us-east-1.amazonaws.com/cdp/development/rotor:latest

      - name: Restart development rotor
        run: |
          aws ecs update-service --cluster cdp-development --service rotor --force-new-deployment

      - name: Restart development console
        run: |
          aws ecs update-service --cluster cdp-development --service console --force-new-deployment

      # Staging

      - name: Configure AWS credentials for staging
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::470689186102:role/ECRBuildAndPushRole
          aws-region: us-east-1
          role-session-name: Github_to_AWS_via_FederatedOIDC

      - name: Sts GetCallerIdentity
        run: aws sts get-caller-identity

      - name: Login to Amazon ECR staging
        id: login-ecr-staging
        uses: aws-actions/amazon-ecr-login@v2

      - name: Push console image to staging
        if: ${{ inputs.push_staging == true }}
        run: |
          docker tag console:${{ github.sha }} 470689186102.dkr.ecr.us-east-1.amazonaws.com/cdp/staging/console:${{ github.sha }}
          docker tag console:${{ github.sha }} 470689186102.dkr.ecr.us-east-1.amazonaws.com/cdp/staging/console:latest
          docker push 470689186102.dkr.ecr.us-east-1.amazonaws.com/cdp/staging/console:${{ github.sha }}
          docker push 470689186102.dkr.ecr.us-east-1.amazonaws.com/cdp/staging/console:latest

      - name: Push rotor image to staging
        if: ${{ inputs.push_staging == true }}
        run: |
          docker tag rotor:${{ github.sha }} 470689186102.dkr.ecr.us-east-1.amazonaws.com/cdp/staging/rotor:${{ github.sha }}
          docker tag rotor:${{ github.sha }} 470689186102.dkr.ecr.us-east-1.amazonaws.com/cdp/staging/rotor:latest
          docker push 470689186102.dkr.ecr.us-east-1.amazonaws.com/cdp/staging/rotor:${{ github.sha }}
          docker push 470689186102.dkr.ecr.us-east-1.amazonaws.com/cdp/staging/rotor:latest

      - name: Restart staging rotor
        if: ${{ inputs.push_staging == true }}
        run: |
          aws ecs update-service --cluster cdp-staging --service rotor --force-new-deployment

      - name: Restart staging console
        if: ${{ inputs.push_staging == true }}
        run: |
          aws ecs update-service --cluster cdp-staging --service console --force-new-deployment

      # Production

      - name: Configure AWS credentials for production
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::495030305857:role/ECRBuildAndPushRole
          aws-region: us-east-1
          role-session-name: Github_to_AWS_via_FederatedOIDC

      - name: Sts GetCallerIdentity
        run: aws sts get-caller-identity

      - name: Login to Amazon ECR production
        id: login-ecr-production
        uses: aws-actions/amazon-ecr-login@v2

      - name: Push console image to production
        if: ${{ inputs.push_production == true }}
        run: |
          docker tag console:${{ github.sha }} 495030305857.dkr.ecr.us-east-1.amazonaws.com/cdp/production/console:${{ github.sha }}
          docker tag console:${{ github.sha }} 495030305857.dkr.ecr.us-east-1.amazonaws.com/cdp/production/console:latest
          docker push 495030305857.dkr.ecr.us-east-1.amazonaws.com/cdp/production/console:${{ github.sha }}
          docker push 495030305857.dkr.ecr.us-east-1.amazonaws.com/cdp/production/console:latest

      - name: Push rotor image to production
        if: ${{ inputs.push_production == true }}
        run: |
          docker tag rotor:${{ github.sha }} 495030305857.dkr.ecr.us-east-1.amazonaws.com/cdp/production/rotor:${{ github.sha }}
          docker tag rotor:${{ github.sha }} 495030305857.dkr.ecr.us-east-1.amazonaws.com/cdp/production/rotor:latest
          docker push 495030305857.dkr.ecr.us-east-1.amazonaws.com/cdp/production/rotor:${{ github.sha }}
          docker push 495030305857.dkr.ecr.us-east-1.amazonaws.com/cdp/production/rotor:latest
