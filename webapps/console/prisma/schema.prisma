generator zod {
  provider = "zod-prisma"
  output   = "./schema"

  relationModel         = false
  modelCase             = "PascalCase"
  modelSuffix           = "DbModel"
  useDecimalJs          = true
  prismaJsonNullability = true
}

generator client {
  provider        = "prisma-client-js"
  extendedIndexes = true
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model EmailLog {
  id         String   @id @default(cuid())
  time       DateTime @default(now())
  email      Json
  error      String?
  previewUrl String?
  messageId  String?
  status     String
}

model UserPreferences {
  id        String      @id @default(cuid())
  createdAt DateTime    @default(now()) /// @zod.custom(z.coerce.date())
  updatedAt DateTime    @default(now()) @updatedAt /// @zod.custom(z.coerce.date())
  userId    String
  user      UserProfile @relation(fields: [userId], references: [id])

  //If preferences relates to workspace,
  workspaceId String?
  workspace   Workspace? @relation(fields: [workspaceId], references: [id])
  //empty or 'server-only'. Server-only means that preferences can't be changed from JS. NOT USED so far
  scope       String?
  preferences Json
}

model UserProfile {
  id                      String                    @id @default(cuid())
  name                    String
  email                   String
  admin                   Boolean?                  @default(false)
  createdAt               DateTime                  @default(now()) /// @zod.custom(z.coerce.date())
  updatedAt               DateTime                  @default(now()) @updatedAt /// @zod.custom(z.coerce.date())
  loginProvider           String
  externalUsername        String?
  externalId              String
  tokens                  UserApiToken[]
  workspaceAccess         WorkspaceAccess[]
  userPreferences         UserPreferences[]
  workspaceUserProperties WorkspaceUserProperties[]
  password                UserPassword?

  @@unique([loginProvider, externalId])
  @@index(externalId)
}

model UserPassword {
  id                String      @id @default(cuid())
  createdAt         DateTime    @default(now()) /// @zod.custom(z.coerce.date())
  updatedAt         DateTime    @default(now()) @updatedAt /// @zod.custom(z.coerce.date())
  userId            String      @unique
  user              UserProfile @relation(fields: [userId], references: [id])
  hash              String
  changeAtNextLogin Boolean     @default(false)
}

model Workspace {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now()) /// @zod.custom(z.coerce.date())
  updatedAt DateTime @default(now()) @updatedAt /// @zod.custom(z.coerce.date())
  name      String
  deleted   Boolean  @default(false)

  slug                    String?
  workspaceAccess         WorkspaceAccess[]
  workspaceOptions        WorkspaceOptions[]
  configurationObject     ConfigurationObject[]
  configurationObjectLink ConfigurationObjectLink[]
  preferences             UserPreferences[]
  workspaceUserProperties WorkspaceUserProperties[]
  featuresEnabled         String[]                  @default([])
  profileBuilders         ProfileBuilder[]

  @@unique(slug)
  @@index(slug)
  @@index(updatedAt(sort: Desc))
}

//additional properties for workspace
model WorkspaceOptions {
  id          String    @id @default(cuid())
  createdAt   DateTime  @default(now()) /// @zod.custom(z.coerce.date())
  updatedAt   DateTime  @default(now()) @updatedAt /// @zod.custom(z.coerce.date())
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id])
  namespace   String
  value       Json

  @@index([workspaceId, namespace])
}

model WorkspaceAccess {
  workspaceId String
  createdAt   DateTime    @default(now()) /// @zod.custom(z.coerce.date())
  updatedAt   DateTime    @default(now()) @updatedAt /// @zod.custom(z.coerce.date())
  userId      String
  workspace   Workspace   @relation(fields: [workspaceId], references: [id])
  user        UserProfile @relation(fields: [userId], references: [id])

  @@id([userId, workspaceId])
}

model WorkspaceUserProperties {
  workspaceId String
  workspace   Workspace   @relation(fields: [workspaceId], references: [id])
  userId      String
  user        UserProfile @relation(fields: [userId], references: [id])
  lastUsed    DateTime    @default(now()) /// @zod.custom(z.coerce.date())

  @@id([workspaceId, userId])
}

model UserApiToken {
  id        String      @id @default(cuid())
  createdAt DateTime    @default(now()) /// @zod.custom(z.coerce.date())
  updatedAt DateTime    @default(now()) @updatedAt /// @zod.custom(z.coerce.date())
  lastUsed  DateTime? /// @zod.custom(z.coerce.date())
  hint      String
  hash      String
  userId    String
  user      UserProfile @relation(fields: [userId], references: [id])
}

model InvitationToken {
  id          String @id @default(cuid())
  workspaceId String

  email     String
  createdAt DateTime @default(now()) /// @zod.custom(z.coerce.date())
  updatedAt DateTime @default(now()) @updatedAt /// @zod.custom(z.coerce.date())
  token     String
  usedBy    String?
}

model ConfigurationObject {
  id          String    @id @default(cuid())
  deleted     Boolean?  @default(false)
  createdAt   DateTime  @default(now()) /// @zod.custom(z.coerce.date())
  updatedAt   DateTime  @default(now()) @updatedAt /// @zod.custom(z.coerce.date())
  type        String
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id])
  config      Json?

  fromLinks              ConfigurationObjectLink[] @relation("from")
  toLinks                ConfigurationObjectLink[] @relation("to")
  ProfileBuilder         ProfileBuilder[]
  ProfileBuilderFunction ProfileBuilderFunction[]

  @@index(updatedAt(sort: Desc))
}

model ConfigurationObjectLink {
  id        String   @id @default(cuid())
  deleted   Boolean? @default(false)
  createdAt DateTime @default(now()) /// @zod.custom(z.coerce.date())
  updatedAt DateTime @default(now()) @updatedAt /// @zod.custom(z.coerce.date())
  type      String?  @default("push")
  data      Json?

  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id])

  fromId String
  from   ConfigurationObject @relation(fields: [fromId], references: [id], name: "to")

  toId String
  to   ConfigurationObject @relation(fields: [toId], references: [id], name: "from")

  @@index(updatedAt(sort: Desc))
  @@index(workspaceId)
}

model ConnectorPackage {
  id          String   @id @default(cuid())
  packageId   String
  packageType String   @default("airbyte")
  meta        Json?
  logoSvg     Bytes?
  createdAt   DateTime @default(now()) /// @zod.custom(z.coerce.date())
  updatedAt   DateTime @default(now()) @updatedAt /// @zod.custom(z.coerce.date())
}

model AuditLog {
  id          String   @id @default(cuid())
  timestamp   DateTime @default(now()) /// @zod.custom(z.coerce.date())
  type        String
  userId      String?
  workspaceId String?
  objectId    String?
  changes     Json?

  @@index(timestamp)
  @@index(type)
}

model GlobalProps {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now()) /// @zod.custom(z.coerce.date())
  updatedAt DateTime @default(now()) @updatedAt /// @zod.custom(z.coerce.date())
  name      String
  value     Json?
}

model source_spec {
  package   String
  version   String
  specs     Json?
  timestamp DateTime @default(now()) @db.Timestamptz(3) /// @zod.custom(z.coerce.date())
  error     String?

  @@id([package, version])
}

model source_check {
  package     String
  version     String
  key         String   @id
  status      String
  description String?
  timestamp   DateTime @default(now()) @db.Timestamptz(3) /// @zod.custom(z.coerce.date())
}

model source_catalog {
  package     String
  version     String
  key         String
  catalog     Json?
  timestamp   DateTime @default(now()) @db.Timestamptz(3) /// @zod.custom(z.coerce.date())
  status      String
  description String?

  @@id([package, version, key])
}

model source_state {
  sync_id   String
  stream    String
  state     Json
  timestamp DateTime @default(now()) @db.Timestamptz(3) /// @zod.custom(z.coerce.date())

  @@id([sync_id, stream])
}

model source_task {
  sync_id     String
  task_id     String   @id
  package     String
  version     String
  started_at  DateTime @default(now()) @db.Timestamptz(3) /// @zod.custom(z.coerce.date())
  updated_at  DateTime @default(now()) @db.Timestamptz(3) /// @zod.custom(z.coerce.date())
  started_by  Json?
  status      String
  description String?
  metrics     Json?
  error       String?

  @@index(sync_id)
  @@index(started_at)
}

model task_log {
  id        String   @id @default(uuid()) @db.Uuid
  level     String
  logger    String
  message   String
  sync_id   String
  task_id   String
  timestamp DateTime @default(now()) @db.Timestamptz(3) /// @zod.custom(z.coerce.date())

  @@index(sync_id)
  @@index(task_id)
  @@index(timestamp)
}

model ProfileBuilder {
  id                             String                   @id @default(cuid())
  name                           String
  deleted                        Boolean?                 @default(false)
  createdAt                      DateTime                 @default(now()) /// @zod.custom(z.coerce.date())
  updatedAt                      DateTime                 @default(now()) @updatedAt /// @zod.custom(z.coerce.date())
  workspaceId                    String
  workspace                      Workspace                @relation(fields: [workspaceId], references: [id])
  intermediateStorageCredentials Json
  destinationId                  String?
  destination                    ConfigurationObject?     @relation(fields: [destinationId], references: [id])
  connectionOptions              Json?
  functions                      ProfileBuilderFunction[]
  version                        Int                      @default(0)
}

model ProfileBuilderFunction {
  id               String              @id @default(cuid())
  createdAt        DateTime            @default(now()) /// @zod.custom(z.coerce.date())
  updatedAt        DateTime            @default(now()) @updatedAt /// @zod.custom(z.coerce.date())
  profileBuilderId String
  profileBuilder   ProfileBuilder      @relation(fields: [profileBuilderId], references: [id])
  functionId       String
  function         ConfigurationObject @relation(fields: [functionId], references: [id])
}

model ProfileBuilderState {
  profileBuilderId      String
  profileBuilderVersion Int
  instanceIndex         Int
  totalInstances        Int
  startedAt             DateTime
  updatedAt             DateTime
  lastTimestamp         DateTime?
  processedUsers        Int
  totalUsers            Int
  errorUsers            Int
  speed                 Float

  @@id([profileBuilderId, profileBuilderVersion, totalInstances, instanceIndex])
}

model ProfileBuilderState2 {
  profileBuilderId String
  updatedAt        DateTime
  fullRebuildInfo  Json?
  queuesInfo       Json?
  metrics          Json?

  @@id([profileBuilderId])
}

model StatusChange {
  id          BigInt   @id @default(autoincrement()) @db.BigInt
  workspaceId String
  actorId     String
  tableName   String
  timestamp   DateTime @default(now()) @db.Timestamptz(3) /// @zod.custom(z.coerce.date())
  status      String
  startedAt   DateTime
  counts      Int      @default(0)
  description String?
  queueSize   Int      @default(0)

  @@index([actorId, tableName])
  @@index(timestamp)
  @@index(startedAt)
}

model NotificationState {
  workspaceId      String
  actorId          String
  tableName        String
  channelId        String
  flappingSince    DateTime? @db.Timestamptz(3) /// @zod.custom(z.coerce.date())
  lastNotification DateTime  @db.Timestamptz(3) /// @zod.custom(z.coerce.date())
  statusChangeId   BigInt
  error            String?

  @@id([channelId, actorId, tableName])
}

model Notification {
  id             String   @id @default(uuid()) @db.Uuid
  workspaceId    String
  actorId        String
  tableName      String
  channelId      String
  statusChangeId BigInt
  timestamp      DateTime @default(now()) @db.Timestamptz(3) /// @zod.custom(z.coerce.date())
  status         String
  error          String?

  @@index([actorId, tableName])
  @@index(timestamp)
}

//model OauthSecrets {
//  id        String   @id @default(cuid())
//  createdAt DateTime @default(now()) /// @zod.custom(z.coerce.date())
//  updatedAt DateTime @default(now()) @updatedAt /// @zod.custom(z.coerce.date())
//  nangoIntegrationId String
//  secrets      Json?
//}
