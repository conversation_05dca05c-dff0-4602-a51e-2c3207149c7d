{"name": "@jitsu-ee/ee-api", "version": "0.0.0", "private": true, "scripts": {"ee-api:dev": "next dev -p 3001", "email:dev": "email dev -p 3901", "build": "next build", "lint": "next lint", "db:update-schema": "dotenv -e ../../.env.local -- ts-node ./scripts/sql-exec.ts ./scripts/sql"}, "dependencies": {"@aws-sdk/client-s3": "^3.418.0", "@clickhouse/client": "^1.10.1", "@react-email/components": "^0.0.33", "@types/node": "^18.15.3", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "crypto-js": "^4.1.1", "dayjs": "^1.11.10", "firebase-admin": "^13.2.0", "intercom-client": "^5.0.0", "is-valid-domain": "^0.1.6", "json5": "^2.2.3", "jsonwebtoken": "^9.0.2", "juava": "workspace:*", "lodash": "^4.17.21", "next": "^15.3.0", "node-fetch-commonjs": "^3.3.2", "pg": "~8.11.6", "react": "^18.3.1", "react-dom": "18.3.1", "resend": "^4.0.0", "stripe": "^11.15.0", "tslib": "^2.6.3", "zod": "^3.23.8"}, "devDependencies": {"@react-email/components": "^0.0.33", "react-email": "^3.0.7", "eslint": "^8.57.0", "eslint-config-next": "^15.3.0", "@types/crypto-js": "^4.1.1", "@types/jsonwebtoken": "^9.0.3", "@types/lodash": "^4.14.185", "@types/pg": "~8.11.15", "@types/stripe": "^8.0.417", "dotenv": "^16.3.1", "raw-loader": "^4.0.2", "ts-node": "^10.9.2", "type-fest": "^3.5.7", "typescript": "^5.6.3"}}