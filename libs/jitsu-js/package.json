{"name": "@jitsu/js", "version": "0.0.0", "description": "", "author": "Jitsu Dev Team <<EMAIL>>", "main": "dist/jitsu.cjs.js", "files": ["dist"], "module": "dist/jitsu.es.js", "types": "dist/jitsu.d.ts", "publishConfig": {"access": "public"}, "license": "MIT", "private": false, "scripts": {"clean": "rm -rf ./dist ./compiled", "test:node": "jest", "test:playwright": "playwright test ./__tests__/playwright", "test": "pnpm run test:node && pnpm run test:playwright", "compile": "tsc -p .", "build": "pnpm run clean && pnpm run compile && rollup -c"}, "devDependencies": {"tslib": "^2.6.3", "@playwright/test": "1.39.0", "@rollup/plugin-commonjs": "^28.0.2", "rollup-plugin-dts": "^6.2.1", "@rollup/plugin-json": "^5.0.1", "@rollup/plugin-multi-entry": "^6.0.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^5.0.1", "@rollup/plugin-terser": "^0.1.0", "@segment/analytics-next": "^1.75.0", "@types/chalk": "^2.2.0", "@types/jest": "^29.2.0", "@types/node": "^18.15.3", "chalk": "^4.1.2", "cookie-parser": "^1.4.7", "ejs": "^3.1.8", "express": "^4.21.2", "jest": "^29.2.2", "node-fetch-commonjs": "^3.3.2", "node-forge": "^1.3.1", "raw-loader": "^4.0.2", "rollup": "^3.29.5", "ts-jest": "29.0.5", "typescript": "^5.6.3", "jsondiffpatch": "workspace:*"}, "peerDependencies": {"@jitsu/protocols": "workspace:*"}, "dependencies": {"@jitsu/event-extractors": "workspace:*", "analytics": "0.8.9"}}