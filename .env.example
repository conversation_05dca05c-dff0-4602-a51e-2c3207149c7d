# Run `docker-compose -f devenv/docker-compose.yml up --force-recreate -d`
# Open:
#   1. http://localhost:3011/ for Redis UI
#   2. http://localhost:3032/ for Kafka UI
######################################
### LOCAL DEVELOPMENT              ###
######################################
#GITHUB_CLIENT_ID=<Make your own client>
#GITHUB_CLIENT_SECRET=<Make your own client>

# GOOGLE_CLIENT_ID=<Make your own client>
# GOOGLE_CLIENT_SECRET=<Make your own client>

#AUTH_OIDC_PROVIDER='{"issuer":"http://localhost:8080/realms/dev_realm","clientId":"dev_client","clientSecret":"your_generated_secret"}'

#DATABASE_URL=postgresql://postgres:postgres-mqf3nzx@localhost:5438/postgres
#REDIS_URL=redis://default:redis-mqf3nzx@localhost:6380
#KAFKA_BOOTSTRAP_SERVERS=localhost:19092

######################################
### USEFUL VARS                    ###
######################################
#LOG_LEVEL=debug
#REACT_EDITOR=idea

