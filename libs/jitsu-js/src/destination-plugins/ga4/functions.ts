import { AnalyticsClientEvent } from "@jitsu/protocols/analytics";
import { Ga4Item } from "./types";
import {
  ExtractedEventData,
  ExtractedProduct,
  extractEventData,
  extractProducts,
  GenericEventMapping,
  getGenericEventMappings,
  processArrayMappings,
} from "@jitsu/event-extractors";

// Constants
const STANDARD_PROPERTIES = ["title", "url", "path", "hash", "search", "width", "height"];
const RESERVED_USER_PROPERTIES = [
  "first_open_time",
  "first_visit_time",
  "last_deep_link_referrer",
  "user_id",
  "first_open_after_install",
];
const RESERVED_PREFIXES = ["google_", "ga_", "firebase_"];

// Types
interface CustomMapping {
  chordEvent: string;
  googleEvent: string;
  parameters?: Array<{
    eventProperty: string;
    googleProperty: string;
  }>;
}

interface CustomMappingResult {
  googleEvent?: string;
  parameters: Record<string, any>;
  itemMappings?: Array<{
    key: string;
    value: string;
    arrayValue: any;
    remainingPath: string;
  }>;
}

interface EventMapping {
  defaultEvent: string;
  getParameters: (extracted: ExtractedEventData) => Record<string, any>;
  hasItems?: boolean;
}

/**
 * Converts a name to GA4-compatible format:
 * - Replaces non-alphanumeric characters with underscores
 * - Converts to lowercase
 * - Truncates to 40 characters
 */
export function handelizeName(name: string): string {
  return name
    .replace(/[^a-zA-Z0-9_]/g, "_")
    .toLowerCase()
    .substring(0, 40);
}

/**
 * Extracts GA4 items from ExtractedProducts
 */
export function getItemsFromExtracted(products: ExtractedProduct[]): Ga4Item[] {
  return products
    .map(product => createItemFromExtracted(product))
    .filter((item): item is Ga4Item => item !== undefined);
}

/**
 * Creates a GA4 item from ExtractedProduct
 */
function createItemFromExtracted(product: ExtractedProduct): Ga4Item | undefined {
  if (!product?.id || !product?.name) return undefined;

  return {
    item_id: product.id,
    item_name: product.name,
    affiliation: product.affiliation,
    coupon: product.coupon,
    creative_name: product.untypedProperties?.creative_name,
    creative_slot: product.untypedProperties?.creative_slot,
    currency: product.untypedProperties?.currency,
    discount: product.untypedProperties?.discount,
    index: product.position,
    item_brand: product.brand,
    item_category: product.category,
    item_category2: product.untypedProperties?.item_category2,
    item_category3: product.untypedProperties?.item_category3,
    item_category4: product.untypedProperties?.item_category4,
    item_category5: product.untypedProperties?.item_category5,
    item_list_id: product.untypedProperties?.item_list_id,
    item_list_name: product.untypedProperties?.item_list_name,
    item_variant: product.variant,
    location_id: product.untypedProperties?.location_id,
    price: product.price,
    promotion_id: product.untypedProperties?.promotion_id,
    promotion_name: product.untypedProperties?.promotion_name,
    quantity: product.quantity,
  };
}

/**
 * Extracts GA4 items from event properties (legacy support)
 */
export function getItems(event: AnalyticsClientEvent): Ga4Item[] {
  const products = extractProducts(event);
  return getItemsFromExtracted(products);
}

/**
 * Extracts user properties from event, filtering out reserved properties
 */
export function getUserProperties(event: AnalyticsClientEvent): Record<string, any> {
  const traits = event.type === "identify" ? event.traits : event.context?.traits;
  if (!traits) return {};

  const userProperties: Record<string, any> = {};

  for (const [key, value] of Object.entries(traits)) {
    if (isValidUserProperty(key)) {
      userProperties[key] = { value };
    }
  }

  return userProperties;
}

/**
 * Checks if a property key is valid for user properties
 */
function isValidUserProperty(key: string): boolean {
  if (RESERVED_USER_PROPERTIES.includes(key)) return false;

  return !RESERVED_PREFIXES.some(prefix => key.startsWith(prefix));
}

/**
 * Resolves the standard value for GA4 events from ExtractedOrderData
 */
export function resolveStandardValueFromOrder(order?: ExtractedEventData["order"]): any {
  if (!order) return undefined;
  return resolvePossibleValue(order.value, order.total, order.revenue);
}

/**
 * Resolves the standard value for GA4 events
 */
export function resolveStandardValue(properties: any): any {
  return resolvePossibleValue(properties?.value, properties?.total, properties?.revenue);
}

/**
 * Returns the first defined value from a list of possible values
 */
export function resolvePossibleValue(...possibleValues: any[]): any {
  for (const value of possibleValues) {
    if (value !== undefined && value !== null) {
      return value;
    }
  }
  return possibleValues[possibleValues.length - 1];
}

/**
 * Removes specified properties from an object
 */
function removeProperties(properties: Record<string, any>, toRemove: string[]): Record<string, any> {
  const result = { ...properties };
  toRemove.forEach(key => delete result[key]);
  return result;
}

/**
 * Finds and processes ALL custom parameters for an event
 * Returns all matching mappings instead of just the first one
 */
export function getAllCustomParameters(
  event: AnalyticsClientEvent,
  customMappings?: CustomMapping[]
): CustomMappingResult[] | null {
  if (!customMappings?.length) return null;

  // Convert GA4-specific mappings to generic format
  const genericMappings: GenericEventMapping[] = customMappings.map(m => ({
    sourceEvent: m.chordEvent,
    targetEvent: m.googleEvent,
    parameters: m.parameters?.map(p => ({
      sourceProperty: p.eventProperty,
      targetProperty: p.googleProperty,
    })),
  }));

  const results = getGenericEventMappings(event, genericMappings, { matchOnEventName: true });

  if (!results || results.length === 0) return null;

  // Convert generic results back to GA4 format
  return results.map(result => ({
    googleEvent: result.targetEvent,
    parameters: result.parameters,
    itemMappings: result.itemMappings,
  }));
}

/**
 * Event mapping definitions
 */
const EVENT_MAPPINGS: Record<string, EventMapping> = {
  "Promotion Clicked": {
    defaultEvent: "select_promotion",
    getParameters: extracted => ({
      creative_name: extracted.untypedProperties.properties?.creative_name,
      creative_slot: extracted.untypedProperties.properties?.creative,
      location_id: extracted.untypedProperties.properties?.position,
      promotion_id: extracted.untypedProperties.properties?.promotion_id,
      promotion_name:
        extracted.untypedProperties.properties?.promotion_name || extracted.untypedProperties.properties?.name,
    }),
    hasItems: true,
  },
  "Product List Viewed": {
    defaultEvent: "view_item_list",
    getParameters: extracted => ({
      item_list_id: extracted.untypedProperties.properties?.list_id,
      item_list_name: extracted.untypedProperties.properties?.category,
    }),
    hasItems: true,
  },
  "Checkout Started": {
    defaultEvent: "begin_checkout",
    getParameters: extracted => ({
      currency: extracted.order?.currency,
      value: resolveStandardValueFromOrder(extracted.order),
      coupon: extracted.order?.coupon,
    }),
    hasItems: true,
  },
  "Order Refunded": {
    defaultEvent: "refund",
    getParameters: extracted => ({
      currency: extracted.order?.currency,
      transaction_id: extracted.order?.id,
      value: resolveStandardValueFromOrder(extracted.order),
      coupon: extracted.order?.coupon,
      shipping: extracted.order?.shipping,
      affiliation: extracted.order?.affiliation,
      tax: extracted.order?.tax,
    }),
    hasItems: true,
  },
  "Product Added": {
    defaultEvent: "add_to_cart",
    getParameters: extracted => ({
      currency: extracted.order?.currency,
      value: resolveStandardValueFromOrder(extracted.order),
    }),
    hasItems: true,
  },
  "Payment Info Entered": {
    defaultEvent: "add_payment_info",
    getParameters: extracted => ({
      currency: extracted.order?.currency,
      value: resolveStandardValueFromOrder(extracted.order),
      coupon: extracted.order?.coupon,
      payment_type: extracted.untypedProperties?.properties?.payment_method,
    }),
    hasItems: true,
  },
  "Product Added to Wishlist": {
    defaultEvent: "add_to_wishlist",
    getParameters: extracted => ({
      currency: extracted.order?.currency,
      value: resolveStandardValueFromOrder(extracted.order),
    }),
    hasItems: true,
  },
  "Product Viewed": {
    defaultEvent: "view_item",
    getParameters: extracted => ({
      currency: extracted.order?.currency,
      value: resolveStandardValueFromOrder(extracted.order),
    }),
    hasItems: true,
  },
  "Signed Up": {
    defaultEvent: "sign_up",
    getParameters: extracted => ({
      method: extracted.untypedProperties?.properties?.type || extracted.untypedProperties?.properties?.method,
    }),
  },
  "Order Completed": {
    defaultEvent: "purchase",
    getParameters: extracted => ({
      currency: extracted.order?.currency,
      transaction_id: extracted.order?.id,
      value: resolveStandardValueFromOrder(extracted.order),
      coupon: extracted.order?.coupon,
      shipping: extracted.order?.shipping,
      affiliation: extracted.order?.affiliation,
      tax: extracted.order?.tax,
    }),
    hasItems: true,
  },
  "Promotion Viewed": {
    defaultEvent: "view_promotion",
    getParameters: extracted => ({
      creative_name: extracted.untypedProperties?.properties?.creative_name,
      creative_slot: extracted.untypedProperties?.properties?.creative,
      location_id: extracted.untypedProperties?.properties?.position,
      promotion_id: extracted.untypedProperties?.properties?.promotion_id,
      promotion_name:
        extracted.untypedProperties?.properties?.promotion_name || extracted.untypedProperties?.properties?.name,
    }),
    hasItems: true,
  },
  "Cart Viewed": {
    defaultEvent: "view_cart",
    getParameters: extracted => ({
      currency: extracted.order?.currency,
      value: resolveStandardValueFromOrder(extracted.order),
    }),
    hasItems: true,
  },
  "Signed In": {
    defaultEvent: "login",
    getParameters: extracted => ({
      method: extracted.untypedProperties?.properties?.type || extracted.untypedProperties?.properties?.method,
    }),
  },
  "Product Removed": {
    defaultEvent: "remove_from_cart",
    getParameters: extracted => ({
      currency: extracted.order?.currency,
      value: resolveStandardValueFromOrder(extracted.order),
    }),
    hasItems: true,
  },
  "Products Searched": {
    defaultEvent: "search",
    getParameters: extracted => ({
      search_term: extracted.untypedProperties?.properties?.query,
    }),
  },
  "Product Clicked": {
    defaultEvent: "select_item",
    getParameters: extracted => ({
      item_list_id: extracted.untypedProperties?.properties?.list_id,
      item_list_name: extracted.untypedProperties?.properties?.category,
    }),
    hasItems: true,
  },
  "Generate Lead": {
    defaultEvent: "generate_lead",
    getParameters: extracted => ({
      currency: extracted.order?.currency,
      value: resolveStandardValueFromOrder(extracted.order),
      params: removeProperties(extracted.untypedProperties?.properties || {}, [
        ...STANDARD_PROPERTIES,
        "value",
        "currency",
      ]),
    }),
  },
};

/**
 * Main function to track events to GA4
 */
export function trackEvent(
  event: AnalyticsClientEvent,
  ids: any,
  userProperties: any,
  gtag: any,
  customMappings?: CustomMapping[]
): void {
  const basePayload = {
    ...ids,
    ...userProperties,
  };

  const extracted = extractEventData(event);

  const customMappingResults = getAllCustomParameters(event, customMappings) || [];

  const eventMapping = EVENT_MAPPINGS[event.event];

  if (eventMapping) {
    // Scenario 1: Known event (with or without custom mappings)
    const mappingsToUse = customMappingResults.length > 0 ? customMappingResults : [null];
    mappingsToUse.forEach(customMapping => {
      handleMappedEvent(eventMapping, basePayload, customMapping, extracted, gtag);
    });
  } else if (customMappingResults.length > 0) {
    // Scenario 2: Unknown event with custom mappings
    customMappingResults.forEach(customMapping => {
      handleCustomEvent(basePayload, customMapping, extracted, gtag);
    });
  } else {
    // Scenario 3: Unknown event without custom mappings
    handleUnmappedEvent(basePayload, extracted, gtag);
  }
}

/**
 * Handles events with predefined mappings
 */
function handleMappedEvent(
  eventMapping: EventMapping,
  basePayload: Record<string, any>,
  customMapping: CustomMappingResult | null,
  extracted: ExtractedEventData,
  gtag: any
): void {
  // Get default parameters
  const defaultParams = {
    ...basePayload,
    ...eventMapping.getParameters(extracted),
  };

  // Add items if the event supports them
  if (eventMapping.hasItems && extracted.order?.products) {
    defaultParams.items = getItemsFromExtracted(extracted.order.products);

    // Set currency on items if available
    if (extracted.order.currency) {
      defaultParams.items.forEach((item: Ga4Item) => {
        item.currency = extracted.order!.currency;
      });
    }
  }

  // Determine event name and merge parameters
  const eventName = customMapping?.googleEvent || eventMapping.defaultEvent;
  const finalParams = customMapping ? { ...defaultParams, ...customMapping.parameters } : defaultParams;

  // Process array mappings for items
  if (customMapping?.itemMappings && finalParams.items) {
    finalParams.items = processArrayMappings(finalParams.items, customMapping.itemMappings);
  }

  gtag("event", eventName, finalParams);
}

/**
 * Handles events that only have custom mappings
 */
function handleCustomEvent(
  basePayload: Record<string, any>,
  customMapping: CustomMappingResult,
  extracted: ExtractedEventData,
  gtag: any
): void {
  const finalParams = {
    ...basePayload,
    ...customMapping.parameters,
  };

  // Process items if there are array mappings
  if (customMapping.itemMappings && extracted.order?.products) {
    const items = getItemsFromExtracted(extracted.order.products);
    if (items.length > 0) {
      finalParams.items = processArrayMappings(items, customMapping.itemMappings);

      // Set currency on items if available
      if (extracted.order.currency) {
        finalParams.items.forEach((item: Ga4Item) => {
          item.currency = extracted.order!.currency;
        });
      }
    }
  }

  gtag("event", customMapping.googleEvent, finalParams);
}

/**
 * Handles events without any mappings
 */
function handleUnmappedEvent(basePayload: Record<string, any>, extracted: ExtractedEventData, gtag: any): void {
  const eventProperties = extracted.untypedProperties.properties || {};

  gtag("event", handelizeName(extracted.eventName), {
    ...basePayload,
    params: removeProperties(eventProperties, STANDARD_PROPERTIES),
    currency: extracted.order?.currency || eventProperties.currency,
    value: resolveStandardValueFromOrder(extracted.order) || resolveStandardValue(eventProperties),
  });
}
