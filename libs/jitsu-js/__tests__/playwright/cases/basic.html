<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>Tracking page</title>
    <script>
      window.jitsuConfig = {
        cookieCapture: {
          ttp: "_ttp",
        },
      };
      window.testOnload = async j => {
        j.identify("john-doe-id-1", { email: "<EMAIL>" });
        j.track("pageLoaded", { trackParam: "trackValue" });
      };
    </script>
    <script
      type="text/javascript"
      src="<%=trackingBase%>/p.js"
      data-onload="testOnload"
      data-debug="true"
      defer
    ></script>
  </head>

  <body>
    <h1>Test</h1>
  </body>
</html>
