import type { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { REVISION_DATE } from "./config";
import type { KlaviyoEventData, KlaviyoProfileAttributes, KlaviyoProfileData } from "./types";
import type { AnalyticsEventAddress } from "../../utils/types";
import { ErrorWithCode } from "../../lib/error-types";
import { processPhoneNumber } from "../../utils";

export const KLAVIYO_MISSING_IDENTIFIER = "klaviyoMissingIdentifier";

export function buildHeaders(authKey: string) {
  return {
    Authorization: `Klaviyo-API-Key ${authKey}`,
    Accept: "application/json",
    revision: REVISION_DATE,
    "Content-Type": "application/json",
  };
}

export function createEventData(event: AnalyticsServerEvent): KlaviyoEventData {
  return {
    data: {
      type: "event",
      attributes: {
        properties: { ...event.properties },
        time: event.timestamp,
        unique_id: event.messageId,
        value: (event.properties?.value ?? event.properties?.revenue ?? event.properties?.total ?? 0) as number,
        metric: {
          data: {
            type: "metric",
            attributes: {
              name: event.event,
            },
          },
        },
        profile: createProfileData(event),
      },
    },
  };
}

export function createProfileData(event: AnalyticsServerEvent): KlaviyoProfileData {
  const attributes: KlaviyoProfileAttributes = {};
  const { anonymousId, userId } = event;
  // event.context.traits is found in track events; event.traits in identifies.
  const address = (event?.context?.traits?.address || event?.traits?.address) as AnalyticsEventAddress;
  const email = (event?.properties?.email || event?.context?.traits?.email || event?.traits?.email) as string;
  let phone = (event?.properties?.phone || event?.context?.traits?.phone || event?.traits?.phone) as string;

  // Klaviyo requires phone numbers to be in E.164 format

  if (phone) {
    phone = processPhoneNumber(phone, address?.country || "US");
  }

  const firstName = (event?.properties?.first_name ||
    event?.context?.traits?.firstName ||
    event?.traits?.firstName) as string;
  const lastName = (event?.properties?.last_name ||
    event?.context?.traits?.lastName ||
    event?.traits?.lastName) as string;
  const name = (event?.context?.traits?.name || event?.traits?.name) as string;

  if (!phone && !email) {
    // Only send events when we know who the user is. The downside to this is
    // that events before identification - generally either via signing in or
    // making a purchase - will never make it to someone's profile. But those
    // events are rarely valuable in flows and this avoids having a profile for
    // every anonymous session, which is a ton of unhelpful noise. This also
    // mirror's how Segment's original Klaviyo destination worked.
    throw new ErrorWithCode(
      "Either phone number or email is required for Klaviyo profile.",
      KLAVIYO_MISSING_IDENTIFIER
    );
  }

  if (anonymousId) attributes.anonymous_id = anonymousId;
  if (email) attributes.email = email;
  if (firstName) attributes.first_name = firstName;
  if (lastName) attributes.last_name = lastName;
  if (phone) attributes.phone_number = phone;
  if (userId) attributes.external_id = userId;
  if (event?.context?.locale) attributes.locale = event.context.locale;

  if (!firstName && !lastName && name && typeof name === "string") {
    attributes.first_name = name.slice(0, name.indexOf(" "));
    attributes.last_name = name.slice(name.indexOf(" ") + 1);
  }

  if (address) {
    attributes.location = {};
    if (address.street) attributes.location.address1 = address.street;
    if (address.street2) attributes.location.address2 = address.street2;
    if (address.city) attributes.location.city = address.city;
    if (address.state) attributes.location.region = address.state;
    if (address.postalCode) attributes.location.zip = address.postalCode;
    if (address.country) attributes.location.country = address.country;
    if (event?.context?.ip) attributes.location.ip = event.context.ip;
  }

  return {
    data: {
      type: "profile",
      attributes,
    },
  };
}
