services:
  kafka:
    tty: true
    image: "bitnami/kafka:3.6.0"
    environment:
      TERM: "xterm-256color"
      KAFKA_CFG_NODE_ID: 0
      KAFKA_CFG_PROCESS_ROLES: controller,broker
      <PERSON><PERSON><PERSON>_CFG_LISTENERS: PLAINTEXT://:9092,CONTROLLER://:9093,EXTERNAL://:19092
      KAFKA_CFG_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,EXTERNAL://localhost:19092
      KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP: CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT,EXTERNAL:PLAINTEXT
      KAFKA_CFG_CONTROLLER_QUORUM_VOTERS: 0@kafka:9093
      KAFKA_CFG_CONTROLLER_LISTENER_NAMES: CONTROLLER
      KAFKA_CFG_AUTO_CREATE_TOPICS_ENABLE: true
      ALLOW_PLAINTEXT_LISTENER: yes
    healthcheck:
      test: ["CMD-SHELL", "kafka-topics.sh --bootstrap-server 127.0.0.1:9092 --list"]
      interval: 5s
      timeout: 10s
      retries: 30
    ports:
      - "19092:19092"

  clickhouse:
    tty: true
    image: clickhouse/clickhouse-server:24.6
    restart: "unless-stopped"
    environment:
      - CLICKHOUSE_DB=newjitsu_metrics
      - CLICKHOUSE_USER=default
      - CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT=1
      - CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD:-default}
    logging:
      options:
        max-size: 10m
        max-file: "3"
    ports:
      - "8123:8123"

  postgres:
    tty: true
    image: postgres:14
    restart: "unless-stopped"
    user: postgres
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-default}
    logging:
      options:
        max-size: 10m
        max-file: "3"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready", "-d", "postgres"]
      interval: 1s
      timeout: 10s
      retries: 10
    ports:
      - "${EXTERNAL_POSTGRES_PORT:-5438}:5432"

  bulker:
    tty: true
    image: jitsucom/bulker:${DOCKER_TAG:-latest}
    platform: linux/arm64/v8
    restart: "unless-stopped"
    environment:
      TERM: "xterm-256color"
      BULKER_KAFKA_BOOTSTRAP_SERVERS: "kafka:9092"
      BULKER_RAW_AUTH_TOKENS: ${BULKER_TOKEN:-default}
      BULKER_CONFIG_SOURCE: "http://host.docker.internal:${CONSOLE_PORT}/api/admin/export/bulker-connections"
      BULKER_CONFIG_SOURCE_HTTP_AUTH_TOKEN: "service-admin-account:${CONSOLE_TOKEN:-default}"
      BULKER_CACHE_DIR: "/tmp/cache"
      BULKER_INTERNAL_TASK_LOG: '{"id":"task_log","metricsKeyPrefix":"syncs","usesBulker":true,"type":"postgres","options":{"mode":"stream"},"credentials":{"host":"postgres","port":5432,"sslMode":"disable","database":"postgres","password":"${POSTGRES_PASSWORD:-default}","username":"postgres","defaultSchema":"newjitsu"}}'
      BULKER_CLICKHOUSE_HOST: "clickhouse:9000"
      BULKER_CLICKHOUSE_PASSWORD: "${CLICKHOUSE_PASSWORD:-default}"
      BULKER_CLICKHOUSE_DATABASE: "newjitsu_metrics"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://bulker:3042/health"]
      interval: 2s
      timeout: 10s
      retries: 15
    depends_on:
      kafka:
        condition: service_healthy

  ingest:
    tty: true
    image: ************.dkr.ecr.us-east-1.amazonaws.com/cdp/development/ingest:${INGEST_TAG:-latest}
    pull_policy: "never"
    restart: "unless-stopped"
    environment:
      TERM: "xterm-256color"
      INGEST_PUBLIC_URL: "${JITSU_INGEST_PUBLIC_URL:-http://localhost:${JITSU_INGEST_PORT:-8080}/}"
      INGEST_KAFKA_BOOTSTRAP_SERVERS: "kafka:9092"
      INGEST_RAW_AUTH_TOKENS: ${BULKER_TOKEN:-default}
      INGEST_REPOSITORY_URL: "http://host.docker.internal:${CONSOLE_PORT}/api/admin/export/streams-with-destinations"
      INGEST_SCRIPT_ORIGIN: "http://host.docker.internal:${CONSOLE_PORT}/api/s/javascript-library"
      INGEST_REPOSITORY_AUTH_TOKEN: "service-admin-account:${CONSOLE_TOKEN:-default}"
      INGEST_CACHE_DIR: "/tmp/cache"
      INGEST_ROTOR_URL: "http://rotor:3401"
      INGEST_ROTOR_AUTH_KEY: ${BULKER_TOKEN:-default}
      INGEST_CLICKHOUSE_HOST: "clickhouse:9000"
      INGEST_CLICKHOUSE_PASSWORD: "${CLICKHOUSE_PASSWORD:-default}"
      INGEST_CLICKHOUSE_DATABASE: "newjitsu_metrics"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://ingest:3049/health"]
      interval: 2s
      timeout: 10s
      retries: 15

  nginx:
    image: nginx:latest
    ports:
      - "8080:8080"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - ingest
    restart: "unless-stopped"
