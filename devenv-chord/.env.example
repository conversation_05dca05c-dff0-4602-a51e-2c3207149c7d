# These env vars are necessary for running the containers in the docker-compose.yml file.
#
# These values must match the equivalents in ../env.local. Recommend you set these values here, then copy
# them to that file.
BULKER_TOKEN=568db7191b761b10e34af181475cb789ae62e8622edd8132ebf8c2792e02017a
CLICKHOUSE_PASSWORD=0129a1bbf9bbe72d697f16e09ffa00b606e2673dff429970a970b6a00cdb63f2
# This must match the PORT value in ../.env.local.
CONSOLE_PORT=4000
CONSOLE_TOKEN=32bf20afba5bf3106b7fd8c8eadbbbcb7cbaa63203abefe88b3d857a06d7e470
POSTGRES_PASSWORD=14583fd6eb8f21c97982d61dfb90809c4f428fbe83c786773f17660115b7f559
# I think one of the pnpm scripts is hardcoded to connect to the DB on port 5438, so recommend you keep that same
# port number here.
EXTERNAL_POSTGRES_PORT=5438
SEED_USER_PASSWORD=changeme
