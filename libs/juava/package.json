{"name": "juava", "version": "0.0.0", "main": "src/index.ts", "types": "src/index.ts", "description": "", "author": "Jitsu Dev Team <<EMAIL>>", "publishConfig": {"access": "public"}, "license": "MIT", "private": false, "scripts": {"compile": "tsc -p .", "build": "pnpm compile", "test": "tsc -p . && jest --verbose"}, "devDependencies": {"@types/jest": "^29.1.1", "@types/node": "^18.15.3", "jest": "^29.1.2", "ts-jest": "29.0.5"}, "dependencies": {"lodash": "^4.17.21", "tslib": "^2.6.3"}}