{"name": "@jitsu-internal/console", "version": "0.0.0", "description": "", "author": "Jitsu Dev Team <<EMAIL>>", "publishConfig": {"access": "public"}, "license": "MIT", "private": false, "scripts": {"prisma": "prisma", "lint": "next lint", "console:dev": "next dev --turbopack", "console:start": "next start", "email:dev": "email dev -p 3901", "dev:rotor": "ts-node service/rotor.ts", "build": "prisma generate && next build", "clean": "rm -rf ./.next ./.turbo", "compile": "tsc -p .", "tool:hash": "ts-node scripts/password-hash.ts", "db:code-gen": "prisma generate", "db:update-schema": "dotenv -e ../../.env.local -- prisma db push", "db:format-schema": "dotenv -e ../../.env.local -- prisma format", "db:update-schema-force": "dotenv -e ../../.env.local -- prisma db push --accept-data-loss", "console:storybook": "storybook dev -p 3010 --no-open"}, "dependencies": {"@ant-design/cssinjs": "^1.21.1", "@clickhouse/client": "^1.10.1", "@dnd-kit/core": "^6.0.8", "@dnd-kit/modifiers": "^6.0.1", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "@fontsource/inter": "^4.5.13", "@fontsource/roboto-mono": "^4.5.8", "@google-cloud/scheduler": "^4.0.0", "@jitsu-internal/webapps-shared": "workspace:*", "@jitsu/core-functions": "workspace:*", "@jitsu/functions-lib": "workspace:*", "@jitsu/jitsu-react": "workspace:*", "@jitsu/js": "workspace:*", "@jitsu/protocols": "workspace:*", "@loadable/component": "^5.15.3", "@monaco-editor/react": "^4.6.0", "@nangohq/frontend": "^0.21.11", "@prisma/client": "^6.5.0", "@react-email/components": "^0.0.34", "@react-email/render": "^1.0.5", "@rjsf/antd": "^5.22.4", "@rjsf/core": "^5.22.4", "@rjsf/utils": "^5.22.4", "@rjsf/validator-ajv8": "^5.22.4", "@tanstack/react-query": "^4.36.1", "@types/cookie": "^1.0.0", "@types/pg-promise": "^5.4.3", "agentkeepalive": "^4.2.1", "ajv": "^8.17.1", "chart.js": "^4.4.1", "classnames": "^2.3.1", "cookie": "1.0.1", "cuid": "^2.1.8", "dayjs": "^1.11.10", "facebook-nodejs-business-sdk": "^21.0.3", "firebase": "^11.4.0", "firebase-admin": "^13.2.0", "google-ads-api": "^17.1.0-rest-beta", "googleapis": "^126.0.1", "highlight.js": "^11.6.0", "ioredis": "^5.3.2", "js-yaml": "^4.1.0", "json5": "^2.2.3", "jsondiffpatch": "^0.5.0", "jsonwebtoken": "^9.0.2", "juava": "workspace:*", "lodash": "^4.17.21", "lucide-react": "^0.447.0", "mjml": "^5.0.0-alpha.6", "mjml-react": "^2.0.8", "monaco-editor": "^0.52.0", "next": "^15.3.0", "next-auth": "^4.24.11", "node-fetch-commonjs": "^3.3.2", "node-sql-parser": "^5.3.8", "nodemailer": "^6.10.1", "object-hash": "^3.0.0", "pg": "~8.11.6", "pg-cursor": "~2.10.1", "prisma": "^6.5.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^4.10.1", "react-json-view": "^1.19.1", "react-use": "^17.4.0", "recharts": "^2.10.4", "stable-hash": "0.0.3", "stopwatch-node": "^1.1.0", "timezones-list": "^3.0.2", "tslib": "^2.6.3", "use-debounce": "^10.0.4", "zod": "^3.23.8", "zod-to-json-schema": "^3.23.2"}, "devDependencies": {"@ant-design/icons": "^5.5.1", "@chromatic-com/storybook": "^1.9.0", "@next/bundle-analyzer": "^15.3.0", "@storybook/addon-essentials": "^8.3.6", "@storybook/addon-interactions": "^8.3.6", "@storybook/addon-links": "^8.3.6", "@storybook/addon-onboarding": "^8.3.6", "@storybook/blocks": "^8.3.6", "@storybook/nextjs": "^8.3.6", "@storybook/react": "^8.3.6", "@storybook/test": "^8.3.6", "@storybook/testing-react": "^2.0.1", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.1.3", "@testing-library/react": "^13.1.1", "@types/cli-progress": "^3.11.0", "@types/js-yaml": "^4.0.5", "@types/lodash": "^4.14.185", "@types/node": "^18.15.3", "@types/nodemailer": "^6.4.17", "@types/pg": "~8.11.15", "@types/pg-query-stream": "^3.4.0", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "ansi-to-html": "^0.7.2", "antd": "^5.22.0", "autoprefixer": "^10.4.20", "dotenv-cli": "^6.0.0", "elliptic": "^6.6.1", "eslint": "^8.57.0", "eslint-config-next": "^15.3.0", "eslint-plugin-storybook": "^0.9.0", "eslint-plugin-unused-imports": "^4.0.1", "http-proxy-middleware": "^2.0.7", "jest": "^28.0.0", "less": "^4.1.2", "less-loader": "^10.2.0", "minimist": "^1.2.7", "postcss": "^8.4.47", "raw-loader": "^4.0.2", "react-email": "^4.0.2", "storybook": "^8.3.6", "tailwindcss": "^3.4.14", "ts-node": "^10.9.2", "type-fest": "^3.5.7", "typescript": "^5.6.3", "zod-prisma": "^0.5.4"}}