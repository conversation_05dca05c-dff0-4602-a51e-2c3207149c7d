import { processPhoneNumber } from "../src/utils";

test("process-phone-number", async () => {
  const phoneNumber = "(*************";
  const countryCode = "US";
  const expectedOutput = "+12025550123";

  const phoneNumberTwo = "0344 335 1801";
  const countryCodeTwo = "GB";
  const expectedOutputTwo = "+443443351801";
  const resultTwo = processPhoneNumber(phoneNumberTwo, countryCodeTwo);

  const result = processPhoneNumber(phoneNumber, countryCode);
  expect(result).toBe(expectedOutput);
  expect(result).not.toBe(phoneNumber);
  expect(result).not.toBeNull();

  expect(resultTwo).toBe(expectedOutputTwo);
  expect(resultTwo).not.toBe(phoneNumberTwo);
  expect(resultTwo).not.toBeNull();
});
