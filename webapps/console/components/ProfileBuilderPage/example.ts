export const testDataExample = JSON.stringify(
  [
    {
      _profile_id: "clewyinv10000mo0f1o1a8sg7",
      type: "page",
      properties: {
        title: "Jitsu",
        url: "https://use.jitsu.com/test",
        path: "/test",
        hash: "",
        search: "",
        width: 2304,
        height: 1186,
        name: "Workspace Page",
      },
      userId: "clewyinv10000mo0f1o1a8sg7",
      anonymousId: "65d98584-5fea-4a2d-8988-b02fe4291f25",
      timestamp: "2024-10-22T15:39:09.456Z",
      sentAt: "2024-10-22T15:39:09.456Z",
      messageId: "1t5bykqr4sj1r59fbvstdw",
      context: {},
      requestIp: "127.0.0.1",
      receivedAt: "2024-10-22T15:39:09.458Z",
    },
    {
      _profile_id: "clewyinv10000mo0f1o1a8sg7",
      type: "track",
      event: "workspace_access",
      properties: {
        workspaceId: "cl9y5kgth0002ccfn3vtqz64g",
        workspaceName: "Test Workspace",
        workspaceSlug: "test",
      },
      userId: "clewyinv10000mo0f1o1a8sg7",
      anonymousId: "7efbac34-c4c0-4e84-9458-e58ec184d337",
      timestamp: "2024-10-22T15:41:24.351Z",
      sentAt: "2024-10-22T15:41:24.351Z",
      messageId: "1hllz5alamc17d4x39cyiu",
      context: {},
      requestIp: "127.0.0.1",
      receivedAt: "2024-10-22T15:41:24.353Z",
    },
    {
      _profile_id: "clewyinv10000mo0f1o1a8sg7",
      type: "identify",
      userId: "clewyinv10000mo0f1o1a8sg7",
      traits: {
        email: "<EMAIL>",
        name: "John Doe",
        externalId: "1234",
      },
      anonymousId: "7efbac34-c4c0-4e84-9458-e58ec184d337",
      timestamp: "2024-10-22T15:41:24.351Z",
      sentAt: "2024-10-22T15:41:24.351Z",
      messageId: "mj2vr0l9wt1849lq6of5y",
      context: {},
      requestIp: "127.0.0.1",
      receivedAt: "2024-10-22T15:41:24.353Z",
    },
  ],
  null,
  2
);
