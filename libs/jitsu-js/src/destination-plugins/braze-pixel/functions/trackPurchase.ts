import { AnalyticsClientEvent } from "@jitsu/protocols/analytics";
import { omit } from "./utils";

export const trackPurchase = (payload: AnalyticsClientEvent, product) => {
  const reservedKeys = ["product_id", "currency", "price", "quantity"];
  const purchaseProperties = omit(payload.properties, reservedKeys);

  const result = window.braze.logPurchase(
    (product.id || product.sku || product.variant).toString(),
    product.price,
    product.currency ?? "USD",
    product.quantity ?? 1,
    purchaseProperties
  );

  if (!result) {
    console.warn("Braze failed to attach purchase to the session for product ", product.product_id);
  }
};
