{"name": "@jitsu/react-example", "version": "0.0.0", "private": true, "dependencies": {"@jitsu/jitsu-react": "workspace:*", "@types/node": "^18.15.3", "lodash": "^4.17.21", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router": "^6.25.1", "react-router-dom": "^6.25.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"react-scripts": "^5.0.1", "typescript": "^5.6.3", "@types/react-dom": "^18.3.0", "@types/react": "^18.3.3", "@types/lodash": "^4.14.185", "tailwindcss": "^3.4.14"}}