import { testJitsuFunction, TestOptions } from "./lib/testing-lib";
import KlaviyoDestination from "../src/functions/klaviyo-destination";
import { KlaviyoCredentials } from "../src/meta";
import { eventsSequence } from "./lib/test-data-chord";

// npx pnpm tsc -p . && TEST_KLAVIYO_DESTINATION=true KLAVIYO_API_KEY=changeme npx pnpm jest --verbose -t 'klaviyo-destination-integration'
test("klaviyo-destination-integration", async () => {
  jest.setTimeout(30000);
  if (!process.env.TEST_KLAVIYO_DESTINATION && !process.env.KLAVIYO_API_KEY) {
    console.log(
      "Skipping Klaviyo destination integration test - TEST_KLAVIYO_DESTINATION or KLAVIYO_API_KEY is not set"
    );
    return;
  }

  const opts: TestOptions<KlaviyoCredentials> = {
    func: KlaviyoDestination,
    config: {
      apiKey: process.env.KLAVIYO_API_KEY || "",
    },
    events: eventsSequence({
      coreEvents: true,
      identifyEvents: false,
      customEvents: false,
      basicEvents: false,
    }),
  };

  await testJitsuFunction(opts);
});
