import { eventsSequence } from "./lib/test-data";
import { testJitsuFunction, TestOptions } from "./lib/testing-lib";
import PosthogDestination from "../src/functions/posthog-destination";
import { PosthogDestinationConfig, POSTHOG_DEFAULT_HOST } from "../src/meta";

// npx pnpm tsc -p . && POSTHOG_API_KEY="phc_tMsSe4ajIA4Mvvrm0TORPFgA3VggdSnrXPebTauW9Po" npx pnpm jest --verbose -t 'posthog-destination-integration'
test("posthog-destination-integration", async () => {
  if (!process.env.POSTHOG_API_KEY) {
    console.log("Skipping posthog destination integration test - POSTHOG_API_KEY is not set");
    return;
  }
  const opts: TestOptions<PosthogDestinationConfig> = {
    func: PosthogDestination,
    config: {
      key: process.env.POSTHOG_API_KEY || "",
      host: process.env.POSTHOG_HOST || POSTHOG_DEFAULT_HOST,
      enableGroupAnalytics: true,
      groupType: "chord_group",
      enableAnonymousUserProfiles: true,
      sendAnonymousEvents: true,
    },
    events: eventsSequence(),
  };
  await testJitsuFunction(opts);
}, 300000);

test("posthog-destination-unit", () => {
  //implement later, when testing library is ready to mock fetch
});
