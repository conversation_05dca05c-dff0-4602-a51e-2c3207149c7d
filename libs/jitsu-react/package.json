{"name": "@jitsu/jitsu-react", "version": "0.0.0", "description": "", "license": "MIT", "main": "dist/index.js", "module": "dist/index.modern.js", "types": "dist/index.d.ts", "source": "src/index.ts", "engines": {"node": ">=10"}, "scripts": {"build": "pnpm compile && microbundle build --jsx React.createElement --no-compress --format es,cjs", "compile": "tsc -p .", "clean": "rm -rf dist"}, "dependencies": {"@jitsu/js": "workspace:*"}, "peerDependencies": {"react": "15.x || 16.x || 17.x || 18.x", "@types/react": "15.x || 16.x || 17.x || 18.x", "react-router-dom": "5.x || 6.x"}, "peerDependenciesMeta": {"react-router-dom": {"optional": true}}, "devDependencies": {"ts-toolbelt": "^9.6.0", "@testing-library/jest-dom": "^6.1.3", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^29.2.3", "@types/node": "^18.15.3", "microbundle": "^0.15.1", "typescript": "^5.6.3", "@babel/plugin-transform-regenerator": "^7.22.10"}, "files": ["dist"]}