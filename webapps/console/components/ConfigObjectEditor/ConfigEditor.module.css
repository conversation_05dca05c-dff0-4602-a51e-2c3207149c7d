.required {
  display: inline-block;
  margin-left: 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.9rem;
  color: rgb(100, 100, 100);
}

.inner:first-child {
  padding-top: 4px;
}

.inner:last-child {
  margin-bottom: 0;
}

.invalidInput input {
  border-color: rgb(255, 0, 0);
}

.listTable :global(th.ant-table-cell) {
  @apply text-textLight font-bold;
  text-transform: uppercase;
}

.listTable :global(th.ant-table-cell::before) {
  content: none !important;
}

.listTable {
  @apply border border-backgroundDark rounded-lg;
}

.help a {
  text-decoration: underline;
}

.nestedObjectField :global(.ant-form-item-label) {
  max-width: 33.8% !important;
  text-transform: capitalize;
}

.nestedObjectField :global(.ant-form-item-control) {
  max-width: 66.2% !important;
}
