import { testJitsuFunction, TestOptions } from "./lib/testing-lib";
import NinetailedCloudDestination from "../src/functions/ninetailed-destination";
import { NinetailedCloudCredentials } from "../src/meta";
import { eventsSequence } from "./lib/test-data-chord";

// npx pnpm tsc -p . && NINETAILED_ORGANIZATION_ID="1ebc9f86-39ef-4503-94c3-ad8dbf1ccc21" NINETAILED_ENVIRONMENT_SLUG="main" npx pnpm jest --verbose -t 'ninetailed-cloud-destination-integration'
test("ninetailed-cloud-destination-integration", async () => {
  if (!process.env.NINETAILED_ORGANIZATION_ID && !process.env.NINETAILED_ENVIRONMENT_SLUG) {
    console.log(
      "Skipping Ninetailed cloud destination integration test - NINETAILED_ORGANIZATION_ID or NINETAILED_ENVIRONMENT_SLUG is not set"
    );
    return;
  }

  const opts: TestOptions<NinetailedCloudCredentials> = {
    func: NinetailedCloudDestination,
    config: {
      organizationId: process.env.NINETAILED_ORGANIZATION_ID || "",
      environmentSlug: process.env.NINETAILED_ENVIRONMENT_SLUG || "main",
    },
    events: eventsSequence(),
  };
  await testJitsuFunction(opts);
});
