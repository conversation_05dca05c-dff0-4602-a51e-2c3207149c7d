import { loadScript } from "../../script-loader";
import { AnalyticsClientEvent } from "@jitsu/protocols/analytics";
import { applyFilters, InternalPlugin } from "../index";
import { trackPurchase } from "./functions/trackPurchase";
import { updateUserProfile } from "./functions/updateUserProfile";
import { trackEvent } from "./functions/trackEvent";
import { endpoints, ORDER_COMPLETED_EVENT } from "./config";
import { BrazeDeviceCredentials, BrazeDeviceState } from "./types";

declare global {
  interface Window {
    braze: any;
    brazeQueue: any[];
  }
}

export const brazeDevicePlugin: InternalPlugin<BrazeDeviceCredentials> = {
  id: "braze-pixel",
  async handle(config, payload: AnalyticsClientEvent) {
    if (!applyFilters(payload, config, "braze-pixel")) {
      return;
    }

    await initBrazeDeviceIfNeeded(config);

    if (payload.type === "identify" && config.enableIdentifyCalls) {
      updateUserProfile(payload);
    }

    if (payload.type === "track" && payload.event != ORDER_COMPLETED_EVENT && config.enableTrackCalls) {
      trackEvent(payload);
    }

    if (payload.type === "track" && payload.event === ORDER_COMPLETED_EVENT && config.enableOrderCompletedCalls) {
      const products = payload.properties?.products as any[];
      products?.forEach(product => {
        trackPurchase(payload, product);
      });
    }
  },
};

async function initBrazeDeviceIfNeeded(config: BrazeDeviceCredentials) {
  if (getBrazeDeviceState() !== "fresh") {
    return;
  }

  const version = config.sdkVersion;

  setBrazeDeviceState("loading");

  let url: string;

  if (version.indexOf("3.") === 0) {
    url = `https://js.appboycdn.com/web-sdk/${version}/appboy.no-amd.min.js`;
  } else {
    url = `https://js.appboycdn.com/web-sdk/${version}/braze.no-module.min.js`;
  }

  (function (a) {
    a.braze = {};
    a.brazeQueue = [];
    for (
      var s =
          "BrazeSdkMetadata DeviceProperties Card Card.prototype.dismissCard Card.prototype.removeAllSubscriptions Card.prototype.removeSubscription Card.prototype.subscribeToClickedEvent Card.prototype.subscribeToDismissedEvent Card.fromContentCardsJson ImageOnly CaptionedImage ClassicCard ControlCard ContentCards ContentCards.prototype.getUnviewedCardCount Feed Feed.prototype.getUnreadCardCount ControlMessage InAppMessage InAppMessage.SlideFrom InAppMessage.ClickAction InAppMessage.DismissType InAppMessage.OpenTarget InAppMessage.ImageStyle InAppMessage.Orientation InAppMessage.TextAlignment InAppMessage.CropType InAppMessage.prototype.closeMessage InAppMessage.prototype.removeAllSubscriptions InAppMessage.prototype.removeSubscription InAppMessage.prototype.subscribeToClickedEvent InAppMessage.prototype.subscribeToDismissedEvent InAppMessage.fromJson FullScreenMessage ModalMessage HtmlMessage SlideUpMessage User User.Genders User.NotificationSubscriptionTypes User.prototype.addAlias User.prototype.addToCustomAttributeArray User.prototype.addToSubscriptionGroup User.prototype.getUserId User.prototype.getUserId User.prototype.incrementCustomUserAttribute User.prototype.removeFromCustomAttributeArray User.prototype.removeFromSubscriptionGroup User.prototype.setCountry User.prototype.setCustomLocationAttribute User.prototype.setCustomUserAttribute User.prototype.setDateOfBirth User.prototype.setEmail User.prototype.setEmailNotificationSubscriptionType User.prototype.setFirstName User.prototype.setGender User.prototype.setHomeCity User.prototype.setLanguage User.prototype.setLastKnownLocation User.prototype.setLastName User.prototype.setPhoneNumber User.prototype.setPushNotificationSubscriptionType User.prototype.setLineId InAppMessageButton InAppMessageButton.prototype.removeAllSubscriptions InAppMessageButton.prototype.removeSubscription InAppMessageButton.prototype.subscribeToClickedEvent FeatureFlag FeatureFlag.prototype.getStringProperty FeatureFlag.prototype.getNumberProperty FeatureFlag.prototype.getBooleanProperty FeatureFlag.prototype.getImageProperty FeatureFlag.prototype.getJsonProperty FeatureFlag.prototype.getTimestampProperty Banner automaticallyShowInAppMessages destroyFeed hideContentCards showContentCards showFeed showInAppMessage deferInAppMessage toggleContentCards toggleFeed changeUser destroy getDeviceId getDeviceId initialize isPushBlocked isPushPermissionGranted isPushSupported logCardClick logCardDismissal logCardImpressions logContentCardImpressions logContentCardClick logCustomEvent logFeedDisplayed logInAppMessageButtonClick logInAppMessageClick logInAppMessageHtmlClick logInAppMessageImpression logPurchase openSession requestPushPermission removeAllSubscriptions removeSubscription requestContentCardsRefresh requestFeedRefresh refreshFeatureFlags requestImmediateDataFlush enableSDK isDisabled setLogger setSdkAuthenticationSignature addSdkMetadata disableSDK subscribeToContentCardsUpdates subscribeToFeedUpdates subscribeToInAppMessage subscribeToSdkAuthenticationFailures toggleLogging unregisterPush wipeData handleBrazeAction subscribeToFeatureFlagsUpdates getAllFeatureFlags logFeatureFlagImpression requestBannersRefresh insertBanner logBannerClick logBannerImpressions getAllBanners subscribeToBannersUpdates isInitialized".split(
            " "
          ),
        i = 0;
      i < s.length;
      i++
    ) {
      for (var m = s[i], k = a.braze, l = m.split("."), j = 0; j < l.length - 1; j++) k = k[l[j]];
      k[l[j]] = new Function(
        "return function " + m.replace(/\./g, "_") + "(){window.brazeQueue.push(arguments); return true}"
      )();
    }
    window.braze.getDeferredInAppMessage = function () {
      return new window.braze.InAppMessage();
    };
    window.braze.getCachedContentCards = function () {
      return new window.braze.ContentCards();
    };
    window.braze.getCachedFeed = function () {
      return new window.braze.Feed();
    };
    window.braze.getUser = function () {
      return new window.braze.User();
    };
    window.braze.getFeatureFlag = function () {
      return new window.braze.FeatureFlag();
    };
    window.braze.getBanner = function () {
      return new window.braze.Banner();
    };
    window.braze.newBannerFromJson = function () {
      return new window.braze.Banner();
    };
  })(window);

  await loadScript(url)
    .then(() => {
      setBrazeDeviceState("loaded");

      const initOptions = {
        baseUrl: endpoints[config.endpoint],
      };

      window.braze.initialize(config.apiKey, initOptions);
      window.braze.openSession();

      if (config.debug) {
        window.braze.toggleLogging();
      }
    })
    .catch(e => {
      console.warn(`Braze SDK init failed: ${e.message}`, e);
      setBrazeDeviceState("failed");
    });
}

function getBrazeDeviceState(): BrazeDeviceState {
  return window["__chordCDPBrazeDeviceState"] || "fresh";
}

function setBrazeDeviceState(s: BrazeDeviceState) {
  window["__chordCDPBrazeDeviceState"] = s;
}
