import { loadScript } from "../../script-loader";
import { AnalyticsClientEvent } from "@jitsu/protocols/analytics";
import { applyFilters, CommonDestinationCredentials, InternalPlugin } from "../index";
import { extractOrderData, extractPageData } from "@jitsu/event-extractors";
import { getItems, resolvePossibleValue, resolveStandardValue } from "../ga4/functions";

export type GoogleAdsDestinationCredentials = {
  conversionId: string;
  clickConversions?: Array<{
    eventName: string;
    conversionLabel: string;
    accountId?: string;
  }>;
  pageLoadConversions?: Array<{
    eventName: string;
    conversionLabel: string;
    accountId?: string;
  }>;
  defaultPageConversion?: string;
  sendPageView?: boolean;
  conversionLinker?: boolean;
  disableAdPersonalization?: boolean;
  floodlightAccountId?: string;
} & CommonDestinationCredentials;

export const googleAdsPlugin: InternalPlugin<GoogleAdsDestinationCredentials> = {
  id: "google-ads-tag",
  async handle(config, payload: AnalyticsClientEvent) {
    if (!applyFilters(payload, config, "google-ads-tag")) {
      return;
    }

    // Use the custom data layer name to avoid conflicts
    const dataLayerName = "dataLayerAds";

    await initGoogleAdsIfNeeded(config, dataLayerName);

    const gtag = window["gtag"];

    const clickConversionsMap = {};
    if (config.clickConversions) {
      for (let i = 0; i < config.clickConversions.length; i++) {
        const conversion = config.clickConversions[i];
        clickConversionsMap[conversion.eventName.toLowerCase()] = {
          label: conversion.conversionLabel,
          accountId: conversion.accountId || config.conversionId,
        };
      }
    }

    const pageLoadConversionsMap = {};
    if (config.pageLoadConversions) {
      for (let i = 0; i < config.pageLoadConversions.length; i++) {
        const conversion = config.pageLoadConversions[i];
        pageLoadConversionsMap[conversion.eventName.toLowerCase()] = {
          label: conversion.conversionLabel,
          accountId: conversion.accountId || config.conversionId,
        };
      }
    }

    switch (payload.type) {
      case "page":
        const pageName =
          payload.properties.title?.toString() ??
          payload.properties.path
            ?.toString()
            ?.replace(/-/g, " ")
            ?.replace(/\b\w/g, match => match.toUpperCase()) ??
          payload.name?.toString();
        const extractedPageData = extractPageData(payload);
        const orderData = extractOrderData(payload);

        // Prepare common page metadata
        const pageMetadata = {
          path: extractedPageData.path || window.location.pathname,
          referrer: extractedPageData.referrer || document.referrer,
          search: extractedPageData.search || window.location.search,
          title: extractedPageData.title || document.title,
          url: extractedPageData.url || window.location.href,
        };

        if (orderData.value !== undefined || orderData.revenue !== undefined || orderData.total !== undefined) {
          pageMetadata["value"] = resolvePossibleValue(orderData.total, orderData.value, orderData.revenue);
        }

        if (orderData.currency) {
          pageMetadata["currency"] = orderData.currency;
        }

        if (orderData.id || orderData.orderId) {
          pageMetadata["transaction_id"] = orderData.id || orderData.orderId;
        }

        // If page is named, check for mapped conversions (case insensitive)
        if (pageName) {
          const lowerPageName = pageName.toLowerCase();
          const mappedConversion = pageLoadConversionsMap[lowerPageName];
          if (mappedConversion) {
            const sendTo = `${mappedConversion.accountId}/${mappedConversion.label}`;
            gtag("event", "conversion", {
              ...pageMetadata,
              name: pageName,
              send_to: sendTo,
            });
          }
        }
        // If no page name but default conversion is set, send that
        else if (config.defaultPageConversion) {
          gtag("event", "conversion", {
            ...pageMetadata,
            send_to: `${config.conversionId}/${config.defaultPageConversion}`,
          });
        }
        break;

      case "track":
        const order = extractOrderData(payload);
        const eventName = payload.event;
        const lowerEventName = eventName.toLowerCase();

        const props = {
          ...(order.currency && { currency: order.currency }),
          ...(resolvePossibleValue(order.total, order.value, order.revenue) && {
            value:
              lowerEventName === "order completed" || lowerEventName === "order_completed"
                ? resolvePossibleValue(order.total, order.value, order.revenue)
                : resolveStandardValue(payload.properties || {}),
          }),
          ...(getItems(payload) && { items: getItems(payload) }),
          ...((order.id || order.orderId) && {
            transaction_id: order.id || order.orderId,
          }),
          ...(order.coupon && { coupon: order.coupon }),
        };

        // Find matching conversion for this event (case insensitive)
        const mappedConversion = clickConversionsMap[lowerEventName];
        if (mappedConversion) {
          const sendTo = `${mappedConversion.accountId}/${mappedConversion.label}`;
          gtag("event", "conversion", {
            ...props,
            send_to: sendTo,
          });
        }
        break;

      case "identify":
        // Not implemented in the provided Segment code
        break;
    }
  },
};

type GtmState = "fresh" | "loading" | "loaded" | "failed";

function getGoogleAdsState(): GtmState {
  return window["__jitsuGoogleAdsState"] || "fresh";
}

function setGoogleAdsState(s: GtmState) {
  window["__jitsuGoogleAdsState"] = s;
}

async function initGoogleAdsIfNeeded(config: GoogleAdsDestinationCredentials, dataLayerName: string) {
  if (getGoogleAdsState() !== "fresh") {
    return;
  }
  setGoogleAdsState("loading");

  window[dataLayerName] = window[dataLayerName] || [];

  window["gtag"] = function () {
    window[dataLayerName].push(arguments);
  };

  // Initialize gtag with required js command
  window["gtag"]("js", new Date());

  // Set ad personalization before any config commands
  if (config.disableAdPersonalization) {
    window["gtag"]("set", "allow_ad_personalization_signals", false);
  }

  // Prepare config settings
  const configSettings = {};
  if (config.sendPageView === false) {
    configSettings["send_page_view"] = false;
  }
  if (config.conversionLinker === false) {
    configSettings["conversion_linker"] = false;
  }

  // Configure Floodlight if provided
  if (config.floodlightAccountId) {
    window["gtag"]("config", config.floodlightAccountId, configSettings);
  }

  // Configure main account
  window["gtag"]("config", config.conversionId, configSettings);

  // Add the data layer name parameter to the script URL if it's not the default
  const dlParam = dataLayerName !== "dataLayer" ? `&l=${dataLayerName}` : "";

  loadScript(`https://www.googletagmanager.com/gtag/js?id=${config.conversionId}${dlParam}`)
    .then(() => {
      setGoogleAdsState("loaded");
    })
    .catch(e => {
      console.warn(`GoogleAds (containerId=${config.conversionId}) init failed: ${e.message}`, e);
      setGoogleAdsState("failed");
    });
}
