import { testJitsuFunction, TestOptions } from "./lib/testing-lib";
import BrazeDestination from "../src/functions/braze-destination";
import { BrazeCredentials } from "../src/meta";
import { eventsSequence } from "./lib/test-data-chord";

// npx pnpm tsc -p . && TEST_BRAZE_DESTINATION=true BRAZE_API_KEY=changeme npx pnpm jest --verbose -t 'braze-destination-integration'
test("braze-destination-integration", async () => {
  if (!process.env.TEST_BRAZE_DESTINATION && !process.env.BRAZE_API_KEY) {
    console.log("Skipping Braze destination integration test - TEST_BRAZE_DESTINATION or BRAZE_API_KEY is not set");
    return;
  }

  const opts: TestOptions<BrazeCredentials> = {
    func: BrazeDestination,
    config: {
      apiKey: process.env.BRAZE_API_KEY || "",
      endpoint: "US-05 : dashboard-05.braze.com",
      appId: "",
      useJitsuAnonymousIdAlias: true,
      sendPageEvents: true,
    },
    events: eventsSequence(),
  };
  await testJitsuFunction(opts);
});
