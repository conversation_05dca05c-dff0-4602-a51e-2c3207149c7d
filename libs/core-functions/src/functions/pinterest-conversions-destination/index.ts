import type { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { JitsuFunction } from "@jitsu/protocols/functions";
import { RetryError } from "@jitsu/functions-lib";

import { PinterestConversionsCredentials } from "../../meta";
import { reportConversionEvent } from "./reportConversionEvent";

/**
 * @see https://github.com/segmentio/action-destinations/tree/main/packages/destination-actions/src/destinations/pinterest-conversions
 */
const PinterestConversionsDestination: JitsuFunction<AnalyticsServerEvent, PinterestConversionsCredentials> = async (
  event,
  ctx
) => {
  const isValidEvent = event.type === "page" || event.type === "track";

  if (isValidEvent) {
    try {
      return await reportConversionEvent(event, ctx);
    } catch (e: any) {
      throw new RetryError(e.message);
    }
  }
};

PinterestConversionsDestination.displayName = "Pinterest-conversions-destination";

PinterestConversionsDestination.description =
  "This function sends events to Pinterest to measure and optimize Pinterest Ads performance.";

export default PinterestConversionsDestination;
