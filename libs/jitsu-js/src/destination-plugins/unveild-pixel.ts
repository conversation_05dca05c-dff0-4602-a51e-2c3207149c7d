import { loadScript, ScriptOptions } from "../script-loader";
import { applyFilters, CommonDestinationCredentials, InternalPlugin } from "./index";

export type UnveildPixelDestinationCredentials = {
  tenantId: string;
} & CommonDestinationCredentials;

declare global {
  interface Window {
    chord?: any;
  }
}

export const unveildPixelPlugin: InternalPlugin<UnveildPixelDestinationCredentials> = {
  id: "unveild-pixel",
  async handle(config, payload) {
    await initUnveildPixelIfNeeded(config, payload);
  },
};

async function initUnveildPixelIfNeeded(config: UnveildPixelDestinationCredentials, payload) {
  if (getUnveildPixelState() !== "fresh") {
    return;
  }
  setUnveildPixelState("loading");

  const options: ScriptOptions = {
    attributes: {
      id: "acquaint-unveild",
      "data-customer-name": "chord",
      "data-external-id": config.tenantId,
      "data-anonymous-user-id": getAnonymousUserId(),
      "data-page-count-cutoff": "1",
    },
  };

  await loadScript("https://sneakpeek-1.s3.us-east-1.amazonaws.com/acquaint_b2b_chord.js", options)
    .then(() => {
      setUnveildPixelState("loaded");
    })
    .catch(e => {
      console.warn(`Unveild Pixel (tenantId=${config.tenantId}) init failed: ${e.message}`, e);
      setUnveildPixelState("failed");
    });
}

type UnveildPixelState = "fresh" | "loading" | "loaded" | "failed";

function getUnveildPixelState(): UnveildPixelState {
  return window["__chordCDPUnveildPixelState"] || "fresh";
}

function setUnveildPixelState(s: UnveildPixelState) {
  window["__chordCDPUnveildPixelState"] = s;
}

function getAnonymousUser(): { anonymousId?: string } | undefined {
  try {
    // For Hydrogen
    if (typeof window.chord?.ccdp === "function") {
      return window.chord.ccdp().user?.();
    }

    // For Autonomy
    if (typeof window.chord?.user === "function") {
      return window.chord.user();
    }
  } catch {
    // fall through and return 'undefined'
  }
  return undefined;
}

function getAnonymousUserId(): string {
  const user = getAnonymousUser();
  return user?.anonymousId ?? "undefined_anonymous_id";
}
