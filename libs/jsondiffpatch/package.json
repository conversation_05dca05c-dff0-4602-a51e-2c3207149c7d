{"name": "jsondiffpatch", "version": "0.6.0", "author": "<PERSON> <<EMAIL>>", "description": "Diff & Patch for Javascript objects", "contributors": ["<PERSON> <<EMAIL>>"], "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["src/index.ts"], "scripts": {"build": "tsc", "clean": "rm -rf ./dist", "type-check": "tsc --noEmit", "lint": "eslint . --ext .ts", "test": "jest --coverage", "prepack": "pnpm build", "prepublishOnly": "pnpm test && pnpm run lint"}, "repository": {"type": "git", "url": "https://github.com/benjamine/jsondiffpatch.git"}, "keywords": ["json", "diff", "patch"], "dependencies": {}, "devDependencies": {"@types/jest": "^29.5.10", "@typescript-eslint/eslint-plugin": "^8.12.2", "@typescript-eslint/parser": "^8.12.2", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "tslib": "^2.6.3", "typescript": "^5.6.3"}, "license": "MIT", "engines": {"node": "^18.0.0 || >=20.0.0"}, "homepage": "https://github.com/benjamine/jsondiffpatch"}