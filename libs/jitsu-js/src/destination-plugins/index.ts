import { AnalyticsClientEvent } from "@jitsu/protocols/analytics";
import { shouldProcessEvent } from "@jitsu/event-extractors";
import { tagPlugin } from "./tag";
import { logrocketPlugin } from "./logrocket";
import { gtmPlugin } from "./gtm";
import { ga4Plugin } from "./ga4";
import { googleAdsPlugin } from "./google-ads";
import { pinterestTagPlugin } from "./pinterest-tag";
import { facebookPixelPlugin } from "./facebook-pixel";
import { unveildPixelPlugin } from "./unveild-pixel";
import { shareASalePixelPlugin } from "./share-a-sale";
import { brazeDevicePlugin } from "./braze-pixel";
import { tiktokPixelPlugin } from "./tiktok-pixel";
import { bingAdsPlugin } from "./bing-ads";
import { ninetailedPlugin } from "./ninetailed";
import { impactPlugin } from "./impact";

export type InternalPlugin<T> = {
  id: string;
  handle(config: T & { debug?: boolean }, payload: AnalyticsClientEvent): Promise<void>;
};

export type CommonDestinationCredentials = {
  hosts?: string;
  events?: string;
  consentRequirements?: string[];
};

export function satisfyFilter(filter: string, subject: string | undefined): boolean {
  return filter === "*" || filter.toLowerCase().trim() === (subject || "").trim().toLowerCase();
}

export function satisfyDomainFilter(filter: string, subject: string | undefined): boolean {
  if (filter === "*") {
    return true;
  }
  subject = subject || "";

  if (filter.startsWith("*.")) {
    return subject.endsWith(filter.substring(1));
  } else {
    return filter === subject;
  }
}

export function applyFilters(
  event: AnalyticsClientEvent,
  creds: CommonDestinationCredentials & { debug?: boolean },
  id?: string
): boolean {
  const { hosts = "*", events = "*", consentRequirements, debug } = creds;

  try {
    const eventsArray = Array.isArray(events) ? events : events.split("\n");
    const hostsArray = Array.isArray(hosts) ? hosts : hosts.split("\n");

    const passesBasicFilters =
      !!hostsArray.find(hostFilter => satisfyDomainFilter(hostFilter, event.context?.page?.host)) &&
      (!!eventsArray.find(eventFilter => satisfyFilter(eventFilter, event.type)) ||
        !!eventsArray.find(eventFilter => satisfyFilter(eventFilter, event.event)));

    if (!passesBasicFilters) {
      if (debug) {
        console.info(`Event Destination ${id} skipped due to basic filters`);
      }
      return false;
    }

    // Check consent requirements if they exist
    if (consentRequirements && consentRequirements.length > 0) {
      const result = shouldProcessEvent(event, consentRequirements);

      if (!result && debug) {
        console.info(
          `Event Destination ${id} skipped due to user consent preferences. Consent requirements: ${JSON.stringify(
            consentRequirements
          )} Consent preferences: ${JSON.stringify(event.context?.consent?.categoryPreferences)}`
        );
      }

      return result;
    }

    return true;
  } catch (e) {
    console.warn(
      `Failed to apply filters: ${e.message}. Typeof events: ${typeof events}, typeof hosts: ${typeof hosts}. Values`,
      events,
      hosts
    );
    throw new Error(
      `Failed to apply filters: ${e.message}. Typeof events: ${typeof events}, typeof hosts: ${typeof hosts}`
    );
  }
}

export const internalDestinationPlugins: Record<string, InternalPlugin<any>> = {
  [tagPlugin.id]: tagPlugin,
  [pinterestTagPlugin.id]: pinterestTagPlugin,
  [gtmPlugin.id]: gtmPlugin,
  [ga4Plugin.id]: ga4Plugin,
  [googleAdsPlugin.id]: googleAdsPlugin,
  [logrocketPlugin.id]: logrocketPlugin,
  [facebookPixelPlugin.id]: facebookPixelPlugin,
  [unveildPixelPlugin.id]: unveildPixelPlugin,
  [brazeDevicePlugin.id]: brazeDevicePlugin,
  [tiktokPixelPlugin.id]: tiktokPixelPlugin,
  [bingAdsPlugin.id]: bingAdsPlugin,
  [ninetailedPlugin.id]: ninetailedPlugin,
  [shareASalePixelPlugin.id]: shareASalePixelPlugin,
  [impactPlugin.id]: impactPlugin,
};
