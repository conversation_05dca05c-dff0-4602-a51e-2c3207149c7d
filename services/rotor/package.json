{"name": "@jitsu-internal/rotor", "version": "0.0.0", "description": "", "author": "Jitsu Dev Team <<EMAIL>>", "publishConfig": {"access": "public"}, "license": "MIT", "private": false, "scripts": {"compile": "rm -rf ./dist && tsc -p . ", "bundle_extenals": "cp dist_package.json ./dist/package.json && cd ./dist/ && npm install", "build": "pnpm compile && webpack && pnpm bundle_extenals", "start": "dotenv -e ../../.env.local -- node dist/main.js", "rotor:dev": "dotenv -e ../../.env.local -- nodemon --watch \"src/**\" --ext \"ts,json,tsx\" --exec \"ts-node src/index.ts\"", "rotor:profile": "dotenv -e ../../.env.local -- nodemon --watch \"src/**\" --ext \"ts,json,tsx\" --exec \"ts-node-dev --inspect -- src/index.ts\"", "test": "tsc -p . && BULKER_URL=dummy BULKER_AUTH_KEY=dummy jest --verbose --forceExit"}, "dependencies": {"undici": "^6.21.2", "@amplitude/ua-parser-js": "^0.7.33", "@aws-sdk/client-s3": "^3.621.0", "@clickhouse/client": "^1.10.1", "@jitsu/core-functions": "workspace:*", "@jitsu/functions-lib": "workspace:*", "@jitsu/event-extractors": "workspace:*", "@maxmind/geoip2-node": "^5.0.0", "@sensejs/kafkajs-zstd-support": "^0.11.0", "@sentry/node": "^9.10.1", "@types/pg-cursor": "^2.7.2", "dayjs": "^1.11.12", "express": "^4.21.2", "ioredis": "^5.4.1", "json5": "^2.2.3", "jsondiffpatch": "workspace:*", "juava": "workspace:*", "kafkajs": "^2.2.4", "kafkajs-snappy": "^1.1.0", "node-cache": "^5.1.2", "node-fetch-commonjs": "^3.3.2", "object-hash": "^3.0.0", "p-queue": "^6.6.2", "pg": "~8.11.6", "pg-cursor": "^2.11.0", "prom-client": "^15.1.3", "semver": "^7.6.3", "tar": "^7.4.3", "tslib": "^2.6.3"}, "devDependencies": {"@babel/preset-env": "^7.25.2", "@babel/preset-react": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@jest/globals": "^29.7.0", "@jest/types": "^29.6.3", "@jitsu/protocols": "workspace:*", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/lodash": "^4.17.7", "@types/node": "^18.15.3", "@types/pg": "~8.11.15", "@types/web": "^0.0.152", "@types/webpack": "^5.28.5", "@webpack-cli/generators": "^3.0.7", "babel-loader": "^9.1.3", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^12.0.2", "declaration-bundler-webpack-plugin": "^1.0.3", "dotenv-cli": "^7.4.2", "jest": "^29.7.0", "lodash": "^4.17.21", "node-loader": "^2.0.0", "nodemon": "^3.1.4", "ts-jest": "^29.2.3", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.6.3", "webpack": "^5.95.0", "webpack-cli": "^5.1.4"}}