import { eventTimeSafeMs } from "../lib";
import { FullContext } from "@jitsu/protocols/functions";
import type { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { BrazeCredentials } from "../../meta";
import omit from "lodash/omit";
import { genders } from "./config";

export function toBrazeGender(gender: string | null | undefined): string | null | undefined {
  if (!gender) {
    return gender;
  }

  const brazeGender = Object.keys(genders).find(key => genders[key].includes(gender.toLowerCase()));
  return brazeGender || gender;
}

export function getAnonymousIdAlias(event: AnalyticsServerEvent, ctx: FullContext<BrazeCredentials>) {
  if (ctx.props.useJitsuAnonymousIdAlias && event.anonymousId) {
    return {
      alias_name: event.anonymousId,
      alias_label: "anonymous_id",
    };
  }
}

export function constructIds(event: AnalyticsServerEvent, ctx: FullContext<BrazeCredentials>) {
  let idObj = {} as any;
  const traits = event.traits || event.context?.traits || {};
  const user_alias = traits.user_alias || event.properties?.user_alias || getAnonymousIdAlias(event, ctx);
  const braze_id = traits.braze_id || event.properties?.braze_id;
  if (event.userId) {
    idObj.external_id = event.userId;
  } else if (user_alias) {
    idObj.user_alias = user_alias;
  } else if (braze_id) {
    idObj.braze_id = braze_id;
  }

  idObj.email = (event?.properties?.email || event?.context?.traits?.email || event?.traits?.email) as string;
  idObj.phone = (event?.properties?.phone || event?.context?.traits?.phone || event?.traits?.phone) as string;

  if (Object.keys(idObj).length === 0) {
    throw new Error('One of "external_id", "user_alias", "braze_id", "email" or "phone" is required.');
  }
  return idObj;
}

export function trackEvent(event: AnalyticsServerEvent, ctx: FullContext<BrazeCredentials>): any {
  return {
    events: [
      {
        ...constructIds(event, ctx),
        app_id: ctx.props.appId,
        name: event.event,
        time: new Date(eventTimeSafeMs(event)).toISOString(),
        properties: event.properties,
        _update_existing_only: false,
      },
    ],
  };
}

export function trackPurchase(event: AnalyticsServerEvent, ctx: FullContext<BrazeCredentials>): any {
  const products = event.properties?.products as any[];
  if (!products || !products.length) {
    return;
  }
  const reservedKeys = ["product_id", "currency", "price", "quantity"];
  const event_properties = omit(event.properties, ["products", "currency"]);
  const base = {
    ...constructIds(event, ctx),
    app_id: ctx.props.appId,
    time: new Date(eventTimeSafeMs(event)).toISOString(),
    _update_existing_only: false,
  };
  return {
    purchases: products.map(product => ({
      ...base,
      product_id: product?.sku || product?.product_id,
      currency: event.properties?.currency,
      price: product?.price,
      quantity: product?.quantity,
      properties: {
        ...omit(product, reservedKeys),
        ...event_properties,
      },
    })),
  };
}

export function updateUserProfile(event: AnalyticsServerEvent, ctx: FullContext<BrazeCredentials>): any {
  const geo = ctx.geo || ({} as any);
  const traits = event.traits || ({} as any);
  const firstName = (event?.properties?.first_name ||
    event?.context?.traits?.firstName ||
    event?.traits?.firstName) as string;
  const lastName = (event?.properties?.last_name ||
    event?.context?.traits?.lastName ||
    event?.traits?.lastName) as string;

  return {
    attributes: [
      {
        ...constructIds(event, ctx),
        country: ctx.geo?.country?.name,
        current_location:
          geo.location?.latitude && geo.location?.longitude
            ? {
                latitude: geo.location?.latitude,
                longitude: geo.location?.longitude,
              }
            : undefined,

        first_name: firstName,
        last_name: lastName,
        home_city: traits.address?.city,
        image_url: traits.avatar,
        time_zone: geo.location?.timezone,
        gender: toBrazeGender(traits.gender),
        ...omit(traits, ["firstName", "lastName", "avatar", "gender", "user_alias", "braze_id"]),
        _update_existing_only: false,
      },
    ],
  };
}

export function identifyUser(event: AnalyticsServerEvent, ctx: FullContext<BrazeCredentials>): any {
  const external_id = event.userId;
  const user_alias = event.traits?.user_alias || getAnonymousIdAlias(event, ctx);
  if (!external_id || !user_alias) {
    return;
  }
  return {
    aliases_to_identify: [
      {
        external_id,
        user_alias,
      },
    ],
    merge_behavior: "merge",
  };
}
