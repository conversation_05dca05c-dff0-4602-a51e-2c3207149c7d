export class ErrorWithCode extends <PERSON>rror {
  code: string;

  /**
   * Creates an instance of ErrorWithCode.
   * @param message - The error message.
   * @param code - The specific error code.
   */
  constructor(message: string, code: string) {
    super(message);
    this.name = "ErrorWithCode";
    this.code = code;

    // Maintain stack trace in V8 environments (Node.js, Chrome)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ErrorWithCode);
    }

    // Set the prototype explicitly for environments where it might be lost
    Object.setPrototypeOf(this, ErrorWithCode.prototype);
  }
}
