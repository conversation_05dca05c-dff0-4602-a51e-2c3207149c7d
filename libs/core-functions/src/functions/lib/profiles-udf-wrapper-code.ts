import { DropRetryErrorName, RetryErrorName } from "@jitsu/functions-lib";

export const functionsLibCode = `const DropRetryErrorName = "Drop & RetryError";
const RetryErrorName = "RetryError";
class RetryError extends Error {
    constructor(message, options) {
        super(message);
        this.name = options?.drop ? "${DropRetryErrorName}" : "${RetryErrorName}";
    }
}

export { DropRetryErrorName, RetryError, RetryErrorName };`;

export const chainWrapperCode = `//** @UDF_FUNCTIONS_IMPORT **//

function isDropResult(result) {
    return result === "drop" || (Array.isArray(result) && result.length === 0) || result === null || result === false;
}

async function runChain(
    chain,
    events,
    user,
    ctx
) {
    const f = chain[0];
    try {
        const result = await f.f(events, user, ctx);
        if (isDropResult(result)) {
            return undefined
        }
        return result
    } catch (err) {
       throw err;
    }
}

const wrappedFunctionChain = async function (eventsProvider, userProvider, ctx) {
    let chain = [];
    //** @UDF_FUNCTIONS_CHAIN **//
    const iterator = {
        [Symbol.iterator]() {
            return {
                next() {
                    const s = eventsProvider.applySyncPromise(undefined, [], {
                        arguments: {copy: true}
                    })
                    if (typeof s === "undefined") {
                        return {done: true};
                    } else {
                        return {done: false, value: JSON.parse(s) };
                    }
                },
            };
        },
        get length() {
            throw new Error("The 'events' object doesn't have the \`length\` property, however you can iterate through it with \`for (const item of events)\` syntax");
        },
        filter() {
            throw new Error("The 'events' object doesn't have the \`filter\` method, however you can iterate through it with \`for (const item of events)\` syntax");
        },
        map() {
            throw new Error("The 'events' object doesn't have the \`map\` method, however you can iterate through it with \`for (const item of events)\` syntax");
        },
        find() {
            throw new Error("The 'events' object doesn't have the \`find\` method, however you can iterate through it with \`for (const item of events)\` syntax");
        },
        some() {
            throw new Error("The 'events' object doesn't have the \`some\` method, however you can iterate through it with \`for (const item of events)\` syntax");
        },
        reduce() {
            throw new Error("The 'events' object doesn't have the \`reduce\` method, however you can iterate through it with \`for (const item of events)\` syntax");
        },
        sort() {
            throw new Error("The 'events' object doesn't have the \`sort\` method, however you can iterate through it with \`for (const item of events)\` syntax");
        },
    };
    let lazyUser;
    function lazyLoad() {
        if (!lazyUser) {
            lazyUser = JSON.parse(userProvider.applySyncPromise(undefined, [], {
                arguments: {copy: true}
            }));
        }
        return lazyUser;
    }

    const user = {
        get anonymousId() {
            return lazyLoad().anonymousId;
        },
        get userId() {
            return lazyLoad().userId;
        },
        get profileId() {
            return lazyLoad().profileId;
        },
        get traits() {
            return lazyLoad().traits;
        },
    };

    return runChain(chain, iterator, user, ctx);
};

const wrappedUserFunction = (id, f, funcCtx) => {

    const log = {
        info: (...args) => {
            _jitsu_log.info.apply(undefined, [funcCtx, ...args], {arguments: {copy: true}});
        },
        error: (...args) => {
            _jitsu_log.error.apply(undefined, [funcCtx, ...args], {arguments: {copy: true}});
        },
        warn: (...args) => {
            _jitsu_log.warn.apply(undefined, [funcCtx, ...args], {arguments: {copy: true}});
        },
        debug: (...args) => {
            _jitsu_log.debug.apply(undefined, [funcCtx, ...args], {arguments: {copy: true}});
        },
    }

    const store = {
        set: async (key, value, opts) => {
            await _jitsu_store.set.apply(undefined, [key, value, opts], {
                arguments: {copy: true},
                result: {promise: true}
            });
        },
        del: async key => {
            await _jitsu_store.del.apply(undefined, [key], {
                arguments: {copy: true},
                result: {promise: true}
            });
        },
        get: async key => {
            const res = await _jitsu_store.get.apply(undefined, [key], {
                arguments: {copy: true},
                result: {promise: true}
            });
            return res ? JSON.parse(res) : undefined;
        },
        ttl: async key => {
            return await _jitsu_store.ttl.apply(undefined, [key], {
                arguments: {copy: true},
                result: {promise: true}
            });
        },
    }

    const getWarehouse = (warehouseId) => {
        return {
            query: async (query, opts) => {
                return await _jitsu_query.apply(undefined, [warehouseId, query, opts], {
                    arguments: {copy: true},
                    result: {promise: true, copy: true}
                });
            },
        };
    }

    const fetch = async (url, opts, extras) => {
        let res
        if (extras) {
            res = await _jitsu_fetch.apply(undefined, [url, opts, {ctx: funcCtx, event: extras.event}], {
                arguments: {copy: true},
                result: {promise: true}
            });
        } else {
            res = await _jitsu_fetch.apply(undefined, [url, opts], {
                arguments: {copy: true},
                result: {promise: true}
            });
        }
        const r = JSON.parse(res);

        return {
            ...r,
            json: async () => {
                return JSON.parse(r.body);
            },
            text: async () => {
                return r.body;
            },
            arrayBuffer: async () => {
                throw new Error("Method 'arrayBuffer' is not implemented");
            },
            blob: async () => {
                throw new Error("Method 'blob' is not implemented");
            },
            formData: async () => {
                throw new Error("Method 'formData' is not implemented");
            },
            clone: async () => {
                throw new Error("Method 'clone' is not implemented");
            },
        };
    }

    return async function (events, user, c) {
        const fetchLogEnabled = _jitsu_fetch_log_level !== "debug" || (funcCtx.function.debugTill && funcCtx.function.debugTill > new Date());
        let ftch = fetch
        if (fetchLogEnabled) {
            ftch = async(url, opts) => {
                return fetch(url, opts, {event});
            }
        }
        const ctx = {
            ...c,
            props: funcCtx.props,
            log,
            getWarehouse,
            store,
            fetch: ftch,
            profileBuilder: {
                id: _jitsu_pbId,
                version: _jitsu_pbVersion,
            }
        };
        return await f(events, user, ctx);
    }
};

export {wrappedFunctionChain};
`;
