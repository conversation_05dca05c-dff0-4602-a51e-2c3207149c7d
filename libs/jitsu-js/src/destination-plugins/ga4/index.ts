import { loadScript } from "../../script-loader";
import { AnalyticsClientEvent } from "@jitsu/protocols/analytics";
import { applyFilters, CommonDestinationCredentials, InternalPlugin } from "../index";
import { trackEvent, getUserProperties } from "./functions";
import {
  extractPageData,
  ExtractedPageData,
  getGenericEventMapping,
  GenericEventMapping,
} from "@jitsu/event-extractors";

export type Ga4DestinationCredentials = {
  debug?: boolean;
  measurementIds?: string;
  autoPageView?: boolean;
  dataLayerName?: string;
  debugMode?: boolean;
  customEventsAndParameters?: Array<{
    chordEvent: string;
    googleEvent: string;
    parameters?: Array<{
      eventProperty: string;
      googleProperty: string;
    }>;
  }>;
} & CommonDestinationCredentials;

export const ga4Plugin: InternalPlugin<Ga4DestinationCredentials> = {
  id: "ga4-tag",
  async handle(config, payload: AnalyticsClientEvent) {
    if (!applyFilters(payload, config, "ga4-tag")) {
      return;
    }

    await initGa4IfNeeded(config, payload);

    const dataLayer = window[config.dataLayerName || "dataLayer"];
    const gtag = function () {
      dataLayer.push(arguments);
    };
    const ids = {
      ...(payload.userId ? { user_id: payload.userId, userId: payload.userId } : {}),
      ...(payload.anonymousId ? { anonymousId: payload.anonymousId } : {}),
    };
    if (payload.userId) {
      // @ts-ignore
      gtag("set", { user_id: payload.userId });
    }

    switch (payload.type) {
      case "page":
        if (config.autoPageView) {
          break;
        }
        const { properties: pageProperties } = payload;

        const extractedPageData = extractPageData(payload) as ExtractedPageData;

        // Check for custom mappings for page events using generic approach
        const genericMappings: GenericEventMapping[] =
          config.customEventsAndParameters?.map(m => ({
            sourceEvent: m.chordEvent,
            targetEvent: m.googleEvent,
            parameters: m.parameters?.map(p => ({
              sourceProperty: p.eventProperty,
              targetProperty: p.googleProperty,
            })),
          })) || [];

        const pageCustomMapping = getGenericEventMapping({ ...payload, event: "page" }, genericMappings, {
          matchOnEventName: false,
        });

        const basePageEvent = {
          page_location: extractedPageData.url,
          page_title: extractedPageData.title || document.title,
          page_path: extractedPageData.path,
          page_hash: pageProperties.hash,
          page_search: extractedPageData.search,
          page_referrer: extractedPageData.referrer || document.referrer,
          ...ids,
        };

        if (pageCustomMapping) {
          const eventName = pageCustomMapping.targetEvent || "page_view";
          const finalParams = {
            ...basePageEvent,
            ...pageCustomMapping.parameters,
          };
          // @ts-ignore
          gtag("event", eventName, finalParams);
        } else {
          // @ts-ignore
          gtag("event", "page_view", basePageEvent);
        }
        break;

      case "track":
        const userProperties = getUserProperties(payload);
        trackEvent(payload, ids, userProperties, gtag, config.customEventsAndParameters);
        break;

      case "identify":
        break;
    }
  },
};

type GtmState = "fresh" | "loading" | "loaded" | "failed";

function getGa4State(): GtmState {
  return window["__jitsuGa4State"] || "fresh";
}

function setGa4State(s: GtmState) {
  window["__jitsuGa4State"] = s;
}

async function initGa4IfNeeded(config: Ga4DestinationCredentials, payload: AnalyticsClientEvent) {
  if (getGa4State() !== "fresh") {
    return;
  }
  setGa4State("loading");

  const dlName = config.dataLayerName || "dataLayer";
  const dlParam = dlName !== "dataLayer" ? "&l=" + dlName : "";

  // to work with both GA4 and GTM
  const tagId = config.measurementIds;

  window[dlName] = window[dlName] || [];
  const gtag = function () {
    window[dlName].push(arguments);
  };
  // @ts-ignore
  gtag("js", new Date());
  gtag(
    // @ts-ignore
    "config",
    tagId,
    {
      ...(payload.userId ? { user_id: payload.userId } : {}),
      ...(!config.autoPageView ? { send_page_view: false } : {}),
      ...(config.debugMode ? { debug_mode: true } : {}),
    }
  );

  loadScript(`https://www.googletagmanager.com/gtag/js?id=${tagId}${dlParam}`)
    .then(() => {
      setGa4State("loaded");
    })
    .catch(e => {
      console.warn(`GA4 (containerId=${config.measurementIds}) init failed: ${e.message}`, e);
      setGa4State("failed");
    });
}
