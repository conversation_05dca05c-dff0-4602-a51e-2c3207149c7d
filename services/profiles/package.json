{"name": "@jitsu-internal/profile-builder", "version": "0.0.0", "description": "", "author": "Jitsu Dev Team <<EMAIL>>", "publishConfig": {"access": "public"}, "license": "MIT", "private": false, "scripts": {"compile": "rm -rf ./dist && tsc -p . ", "bundle_extenals": "cp dist_package.json ./dist/package.json && cd ./dist/ && npm install", "build": "pnpm compile && webpack && pnpm bundle_extenals", "start": "dotenv -e ../../.env.local -- node dist/main.js", "profiles:dev": "dotenv -e ../../.env.local -- nodemon --watch \"src/**\" --ext \"ts,json,tsx\" --exec \"ts-node src/index.ts\"", "profiles:profile": "dotenv -e ../../.env.local -- nodemon --watch \"src/**\" --ext \"ts,json,tsx\" --exec \"ts-node-dev --inspect -- src/index.ts\""}, "dependencies": {"mongodb": "^6.16.0", "isolated-vm": "5.0.1", "@jitsu/core-functions": "workspace:*", "@jitsu/functions-lib": "workspace:*", "juava": "workspace:*", "node-cache": "^5.1.2", "p-queue": "^6.6.2", "express": "^4.21.2", "pg": "~8.11.6", "pg-cursor": "^2.11.0", "prom-client": "^15.1.3", "tslib": "^2.6.3", "@confluentinc/kafka-javascript": "^1.3.0", "@clickhouse/client": "^1.10.1"}, "devDependencies": {"@babel/preset-env": "^7.25.2", "@babel/preset-react": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@jitsu/protocols": "workspace:*", "@types/jest": "^29.5.11", "@types/lodash": "^4.17.7", "@types/node": "^18.15.3", "@types/pg": "~8.11.15", "@types/pg-cursor": "^2.7.2", "@types/web": "^0.0.152", "@types/webpack": "^5.28.5", "@types/express": "^4.17.21", "@webpack-cli/generators": "^3.0.7", "babel-loader": "^9.1.3", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^12.0.2", "declaration-bundler-webpack-plugin": "^1.0.3", "dotenv-cli": "^7.4.2", "jest": "^29.7.0", "@jest/globals": "^29.7.0", "@jest/types": "^29.6.3", "lodash": "^4.17.21", "node-loader": "^2.0.0", "nodemon": "^3.1.4", "ts-jest": "^29.2.3", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.6.3", "webpack": "^5.95.0", "webpack-cli": "^5.1.4"}}