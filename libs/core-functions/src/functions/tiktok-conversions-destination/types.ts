/**
 * TikTok Conversions API type definitions
 * @see https://business-api.tiktok.com/portal/docs?id=1740858531237890
 */

/**
 * Main request structure for TikTok Conversions API
 */
export interface TikTokConversionsRequest {
  event_source: string;
  event_source_id: string;
  partner_name?: string;
  test_event_code?: string;
  data: TikTokConversionsData[];
}

/**
 * Conversion event data structure
 */
export interface TikTokConversionsData {
  event: string;
  event_time: number;
  event_id?: string;
  user: TikTokConversionsUser;
  properties: TikTokConversionsProperties;
  page?: TikTokConversionsPage;
  limited_data_use: boolean;
}

/**
 * Page information related to the conversion event
 */
export interface TikTokConversionsPage {
  url?: string;
  referrer?: string;
}

/**
 * User information related to the conversion event
 */
export interface TikTokConversionsUser {
  external_id: string[];
  phone: string[];
  email: string[];
  ttp?: string;
  lead_id?: string;
  ip?: string;
  user_agent?: string;
  locale?: string;
  first_name?: string;
  last_name?: string;
  city?: string;
  state?: string;
  country?: string;
  zip_code?: string;
  ttclid?: string;
}

/**
 * Properties of the conversion event
 */
export interface TikTokConversionsProperties {
  contents: TikTokConversionsContent[];
  content_type?: string;
  currency?: string;
  value?: number;
  query?: string;
  description?: string;
  order_id?: string;
  shop_id?: string;
}

/**
 * Content details for products in the conversion event
 */
export interface TikTokConversionsContent {
  price?: number;
  quantity?: number;
  content_category?: string;
  content_id?: string;
  content_name?: string;
  brand?: string;
}
