import type { AnalyticsServerEvent, AnalyticsClientEvent, AnalyticsContext } from "@jitsu/protocols/analytics";

/**
 * Core types for extracted data
 */

export interface ExtractedUserData {
  id?: string;
  anonymousId?: string;
  email?: string;
  phone?: string;
  firstName?: string;
  lastName?: string;
  name?: string;
  gender?: string;
  birthday?: string;
  address?: ExtractedAddress;
  ip?: string;
  userAgent?: string;
  locale?: string;
  externalIds?: Record<string, string>;
  deviceType?: string;
  deviceManufacturer?: string;
  deviceModel?: string;
  deviceCarrier?: string;
  deviceOs?: string;
  deviceOsVersion?: string;
  advertisingId?: string;
  adTrackingEnabled?: boolean;
  deviceLatitude?: number;
  deviceLongitude?: number;
}

export interface ExtractedAddress {
  street?: string;
  street2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  zip?: string;
  country?: string;
  company?: string;
  phone?: string;
  alternativePhone?: string;
  name?: string;
}

export interface ExtractedProduct {
  id?: string;
  product_id?: string;
  sku?: string;
  name?: string;
  subtotal?: number;
  price?: number;
  quantity?: number;
  category?: string;
  brand?: string;
  variant?: string;
  affiliation?: string;
  position?: number;
  url?: string;
  image_url?: string;
  description?: string;
  coupon?: string;
  line_item_id?: string;
  slug?: string;
  bundle?: boolean;
  option_values?: string[];
  variant_metadata?: Record<string, any>;
  untypedProperties?: Record<string, any>;
}

export interface ExtractedOrderData {
  id?: string;
  orderId?: string;
  orderName?: string;
  total?: number;
  revenue?: number;
  value?: number;
  subtotal?: number;
  tax?: number;
  shipping?: number;
  discount?: number;
  currency?: string;
  products?: ExtractedProduct[];
  coupon?: string;
  affiliation?: string;
  checkoutId?: string;
  checkoutType?: string;
  orderDate?: string;
  shippingCity?: string;
  shippingState?: string;
  shippingCountry?: string;
  shippingZipcode?: string;
  isRecurring?: boolean;
  hasSubscription?: boolean;
  channel?: string;
  campaignId?: string | number;
  recipient?: string;
  share_via?: string;
  share_message?: string;
}

export interface ExtractedPageData {
  url?: string;
  referrer?: string;
  title?: string;
  path?: string;
  search?: string;
  params?: Record<string, string>;
  host?: string;
  referring_domain?: string;
}

export interface ExtractedAppData {
  name?: string;
  namespace?: string;
  version?: string;
}

export interface ExtractedEventData {
  user: ExtractedUserData;
  order?: ExtractedOrderData;
  page?: ExtractedPageData;
  app?: ExtractedAppData;
  messageId?: string;
  timestamp?: string;
  eventName?: string;
  eventType?: string;
  untypedProperties?: AnalyticsServerEvent | AnalyticsClientEvent;
}

/**
 * Extract all relevant data from an analytics event
 */
export function extractEventData(event: AnalyticsServerEvent | AnalyticsClientEvent): ExtractedEventData {
  return {
    user: extractUserData(event),
    order: extractOrderData(event),
    page: extractPageData(event),
    app: extractAppData(event),
    messageId: event.messageId,
    timestamp: formatString(event.timestamp),
    eventName: event.event,
    eventType: event.type,
    untypedProperties: event,
  };
}

/**
 * Extract user data from an analytics event
 */
export function extractUserData(event: AnalyticsServerEvent | AnalyticsClientEvent): ExtractedUserData {
  const properties = event.properties || {};
  const context = (event.context as AnalyticsContext) || {};
  const contextTraits = context.traits || {};
  const eventTraits = event.traits || {};
  const metadata = (properties.metadata || {}) as Record<string, any>;

  const birthday = properties.birthday || contextTraits.birthday || eventTraits.birthday;

  const email = properties.email || contextTraits.email || eventTraits.email;
  const phone = properties.phone || contextTraits.phone || eventTraits.phone;
  const gender = properties.gender || contextTraits.gender || eventTraits.gender;

  const externalIds: Record<string, string> = {};
  if (metadata.customer_id) {
    externalIds.customer_id = String(metadata.customer_id);
  }

  const addressSources = [properties.address, contextTraits.address, eventTraits.address].filter(
    Boolean
  ) as ExtractedAddress[];

  // Find each property once instead of multiple times
  const findAddressProp = (prop: keyof ExtractedAddress) => {
    const source = addressSources.find(src => src?.[prop]);
    return source ? formatString(source[prop]) : undefined;
  };

  const address: ExtractedAddress = {
    street: findAddressProp("street"),
    street2: findAddressProp("street2"),
    city: findAddressProp("city"),
    state: findAddressProp("state"),
    postalCode: findAddressProp("postalCode"),
    zip: findAddressProp("zip"),
    country: findAddressProp("country"),
    company: findAddressProp("company"),
    phone: findAddressProp("phone"),
    alternativePhone: findAddressProp("alternativePhone"),
    name: findAddressProp("name"),
  };

  const firstName =
    properties.first_name || contextTraits.first_name || eventTraits.first_name || address?.name?.split(" ")[0];
  const lastName =
    properties.last_name || contextTraits.last_name || eventTraits.last_name || address?.name?.split(" ")[1];

  const device = context?.device;
  const os = context?.os;
  const network = context?.network;
  const location = context?.location || context?.geo?.location;

  return {
    id: formatString(event.userId),
    anonymousId: formatString(event.anonymousId),
    email: formatString(email),
    phone: formatString(phone),
    firstName: formatString(firstName),
    lastName: formatString(lastName),
    name: firstName || lastName ? formatString(`${firstName || ""} ${lastName || ""}`.trim()) : undefined,
    gender: formatString(gender),
    birthday: formatString(birthday),
    address: Object.values(address).some(Boolean) ? address : undefined,
    ip: getServerEventField(event, "requestIp") || context.ip,
    userAgent: context.userAgent || context.user_agent,
    locale: context.locale,
    externalIds: externalIds,
    deviceType: device.type,
    deviceManufacturer: device.manufacturer,
    deviceModel: device.model,
    deviceCarrier: network?.carrier,
    deviceOs: os.name,
    deviceOsVersion: os.version,
    advertisingId: device.advertisingId,
    adTrackingEnabled: device.adTrackingEnabled,
    deviceLatitude: location?.latitude,
    deviceLongitude: location?.longitude,
  };
}

export function extractAppData(event: AnalyticsServerEvent | AnalyticsClientEvent): ExtractedAppData | undefined {
  const context = event.context as AnalyticsContext;
  const app = context.app;
  if (!app) return undefined;

  return {
    name: app.name,
    namespace: app.namespace,
    version: app.version,
  };
}

/**
 * Extract URL parameters from a URL string
 */
export function extractUrlParameters(urlString?: string): Record<string, string> {
  if (!urlString) return {};

  try {
    const url = new URL(urlString);
    const params: Record<string, string> = {};

    url.searchParams.forEach((value, key) => {
      params[key] = value;
    });

    return params;
  } catch (error) {
    console.warn(`Failed to parse URL: ${urlString}`, error);
    return {};
  }
}

/**
 * Extract page data from an analytics event
 */
export function extractPageData(event: AnalyticsServerEvent | AnalyticsClientEvent): ExtractedPageData {
  const context = event.context as AnalyticsContext;
  const pageContext = context?.page || {};
  const properties = event.properties || {};

  const url = pageContext.url || properties.url;
  const params = extractUrlParameters(String(url));

  return {
    url: String(url),
    referrer: String(pageContext.referrer || properties.referrer),
    title: String(pageContext.title || properties.title),
    path: pageContext.path,
    search: pageContext.search,
    params: params,
    host: pageContext.host,
  };
}

/**
 * Extract product data from a product object
 *
 * @param product - The product object to extract data from
 * @returns Extracted product data or null if invalid
 */
export function extractProductData(product: Record<string, any>): ExtractedProduct | null {
  if (!product || typeof product !== "object") {
    return null;
  }

  // Handle different product ID formats
  const productId = product.product_id || product.id || product.sku || product.variant;
  if (!productId) {
    return null;
  }

  return {
    id: String(productId),
    product_id: product.product_id ? String(product.product_id) : undefined,
    sku: product.sku ? String(product.sku) : undefined,
    name: product.name || product.product_name,
    price: product.price !== undefined ? Number(product.price) : undefined,
    quantity: product.quantity !== undefined ? Number(product.quantity) : 1,
    category: product.category || product.product_category,
    brand: product.brand,
    variant: product.variant,
    affiliation: product.affiliation,
    position: product.position !== undefined ? Number(product.position) : undefined,
    url: product.url,
    image_url: product.image_url,
    description: product.description,
    coupon: product.coupon,
    line_item_id: product.line_item_id,
    slug: product.slug,
    bundle: product.bundle,
    option_values: ensureArray(product.option_values),
    variant_metadata: product.variant_metadata,
    untypedProperties: product,
  };
}

/**
 * Extract products from an analytics event
 */
export function extractProducts(event: AnalyticsServerEvent | AnalyticsClientEvent): ExtractedProduct[] {
  const properties = event.properties || {};
  const products: ExtractedProduct[] = [];

  // Handle products array
  if (Array.isArray(properties.products) && properties.products.length > 0) {
    for (const product of properties.products) {
      if (typeof product === "object" && product !== null) {
        const extractedProduct = extractProductData(product);
        if (extractedProduct) {
          products.push(extractedProduct);
        }
      }
    }
  }
  // Handle single product case
  else if (properties.product_id || properties.sku || properties.variant) {
    const extractedProduct = extractProductData(properties);
    if (extractedProduct) {
      products.push(extractedProduct);
    }
  }

  return products;
}

/**
 * Extract order data from an analytics event
 */
export function extractOrderData(event: AnalyticsServerEvent | AnalyticsClientEvent): ExtractedOrderData | undefined {
  const properties = event.properties || {};
  const products = extractProducts(event);

  // Extract this calculation to a separate function
  const value = calculateOrderValue(properties, products);

  const meta = properties.meta as { i18n?: { currency?: string } } | undefined;
  const currency = properties.currency || meta?.i18n?.currency || "USD";

  return {
    id: formatString(properties.order_id || properties.id),
    orderId: formatString(properties.cart_id || properties.order_id || properties.id),
    orderName: formatString(properties.order_name),

    total: formatNumber(properties.total) ?? (value !== undefined ? formatNumber(value) : undefined),
    revenue: formatNumber(properties.revenue),
    value: formatNumber(value),
    subtotal: properties.subtotal !== undefined ? Number(properties.subtotal) : undefined,
    tax: properties.tax !== undefined ? Number(properties.tax) : undefined,
    shipping: properties.shipping !== undefined ? Number(properties.shipping) : undefined,
    discount: properties.discount !== undefined ? Number(properties.discount) : undefined,
    currency: formatString(currency),
    products: products.length > 0 ? products : undefined,
    coupon: formatString(properties.coupon),
    affiliation: formatString(properties.affiliation),
    checkoutId: formatString(properties.checkout_id),
    checkoutType: formatString(properties.checkout_type),
    orderDate: formatString(properties.order_date),
    shippingCity: formatString(properties.shipping_city),
    shippingState: formatString(properties.shipping_state),
    shippingCountry: formatString(properties.shipping_country),
    shippingZipcode: formatString(properties.shipping_zipcode),
    isRecurring: formatBoolean(properties.is_recurring),
    hasSubscription: formatBoolean(properties.has_subscription),
    channel: formatString(properties.channel),
    campaignId: formatString(properties.campaign_id),
    recipient: formatString(properties.recipient),
    share_message: formatString(properties.share_message),
    share_via: formatString(properties.share_via),
  };
}

// Helper function for calculating order value
function calculateOrderValue(properties: Record<string, any>, products: ExtractedProduct[]): number | undefined {
  let value = properties.value || properties.total || properties.price;

  if ((value === undefined || value === 0) && products.length > 0) {
    value = products.reduce((sum, item) => {
      const price = item.price || 0;
      const quantity = item.quantity || 1;
      return sum + price * quantity;
    }, 0);
  }

  return value !== undefined ? Number(value) : undefined;
}

/**
 * Format a value as an array if it's not already
 */
export function ensureArray<T>(value: T | T[] | undefined): T[] | undefined {
  if (value === undefined) return undefined;
  return Array.isArray(value) ? value : [value];
}

/**
 * Format a value as a string
 */
export function formatString(value: any): string | undefined {
  if (value === undefined || value === null) return undefined;
  return String(value);
}

/**
 * Format a value as a number
 */
export function formatNumber(value: any): number | undefined {
  if (value === undefined || value === null) return undefined;

  // Handle string values that might have currency symbols or commas
  if (typeof value === "string") {
    value = value.replace(/[^0-9.-]+/g, "");
  }

  const num = Number(value);
  return isNaN(num) ? undefined : num;
}

/**
 * Format a value as a boolean
 */
export function formatBoolean(value: any): boolean | undefined {
  if (value === undefined || value === null) return undefined;
  return Boolean(value);
}

/**
 * Safely access fields from AnalyticsServerEvent
 *
 * @param event - Analytics event object
 * @param field - Field name to access (supports dot notation for nested fields)
 * @returns The value of the field if it exists, otherwise undefined
 */
export function getServerEventField(event: AnalyticsClientEvent | AnalyticsServerEvent, field: string): any {
  if (!event || !field) {
    return undefined;
  }

  if (!field.includes(".")) {
    return event[field as keyof typeof event];
  }

  // Handle nested fields with dot notation
  return field.split(".").reduce((obj, path) => {
    return obj && typeof obj === "object" ? obj[path] : undefined;
  }, event as AnalyticsServerEvent);
}
