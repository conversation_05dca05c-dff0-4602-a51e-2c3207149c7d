@keyframes dotElasticAfter {
  0% {
    transform: scale(1, 1);
  }
  25% {
    transform: scale(1, 1);
  }
  50% {
    transform: scale(1, 0.67);
  }
  75% {
    transform: scale(1, 1.5);
  }
  100% {
    transform: scale(1, 1);
  }
}

@keyframes dotElasticBefore {
  0% {
    transform: scale(1, 1);
  }
  25% {
    transform: scale(1, 1.5);
  }
  50% {
    transform: scale(1, 0.67);
  }
  75% {
    transform: scale(1, 1);
  }
  100% {
    transform: scale(1, 1);
  }
}
@keyframes dotElastic {
  0% {
    transform: scale(1, 1);
  }
  25% {
    transform: scale(1, 1);
  }
  50% {
    transform: scale(1, 1.5);
  }
  75% {
    transform: scale(1, 1);
  }
  100% {
    transform: scale(1, 1);
  }
}

.dotElasticComponent {
  position: relative;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: currentcolor;
  color: currentcolor;
  animation: dotElastic 1s infinite linear;
}

.dotElasticComponent::before,
.dotElasticComponent::after {
  content: "";
  display: inline-block;
  position: absolute;
  top: 0;
}

.dotElasticComponent::before {
  left: -15px;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: currentcolor;
  color: currentcolor;
  animation: dotElasticBefore 1s infinite linear;
}

.dotElasticComponent::after {
  left: 15px;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: currentcolor;
  color: currentcolor;
  animation: dotElasticAfter 1s infinite linear;
}
