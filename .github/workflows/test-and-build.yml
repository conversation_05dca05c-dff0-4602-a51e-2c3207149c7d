name: Jitsu 2.0 Test and Build
on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  workflow_dispatch:

env:
  #container jitsucom/node18builder:latest has playwright browsers binaries preinstalled on this path:
  PLAYWRIGHT_BROWSERS_PATH: /root/.cache/ms-playwright

jobs:
  build:
    name: Build Project
    container: jitsucom/node22builder:latest
    timeout-minutes: 60
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - uses: actions/cache@v4
        name: Setup pnpm cache
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies with pnpm
        run: pnpm install --no-frozen-lockfile
      - name: Check code format
        run: pnpm format:check:all
      - name: Run linter
        run: pnpm lint
      - name: Build project
        run: pnpm build
      - name: Run Tests
        run: pnpm test
      - uses: actions/upload-artifact@v4
        if: always()
        with:
          path: libs/jitsu-js/playwright/artifacts
          name: artifacts/@jitsu/js
          retention-days: 30
      - name: Notify Slack
        uses: rtCamp/action-slack-notify@v2
        # Do not send notification for PRs, feature branches, or forks
        if: ${{ env.SLACK_WEBHOOK != '' && github.ref == 'refs/heads/newjitsu' }}
        env:
          SLACK_USERNAME: "GitHub Actions"
          SLACK_WEBHOOK: ${{ secrets.SLACK_DEV_CHANNEL }}
          SLACK_COLOR: ${{ job.status }}
          MSG_MINIMAL: "true"
          SLACK_ICON_EMOJI: ":github-mark:"
          SLACK_MESSAGE: "${{ job.status }}: Jitsu 2.0 Build: ${{ github.event.head_commit.message }}"
