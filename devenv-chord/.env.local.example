# These are environment variables necessary for building/running the Console Docker image.
# Copy this file to the root of the project as .env.local and fill in the values.

# These env vars must match the equivalents in devenv-chord/.env. The easiest thing to do is to set those variables
# in that file, then copy those values to this file.
BULKER_TOKEN=568db7191b761b10e34af181475cb789ae62e8622edd8132ebf8c2792e02017a
CLICKHOUSE_PASSWORD=0129a1bbf9bbe72d697f16e09ffa00b606e2673dff429970a970b6a00cdb63f2
CONSOLE_TOKEN=32bf20afba5bf3106b7fd8c8eadbbbcb7cbaa63203abefe88b3d857a06d7e470
POSTGRES_PASSWORD=14583fd6eb8f21c97982d61dfb90809c4f428fbe83c786773f17660115b7f559
POSTGRES_PORT=5438

BULKER_AUTH_KEY=${BULKER_TOKEN}
BULKER_URL=http://localhost:3042
CLICKHOUSE_HOST=localhost:8123
CLICKHOUSE_DATABASE=newjitsu_metrics
CONSOLE_RAW_AUTH_TOKENS=${CONSOLE_TOKEN}
DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@localhost:${POSTGRES_PORT}/postgres?schema=newjitsu
GOOGLE_CLIENT_ID=************-669cv5qpgqd724jt5grto6aljbgtdevi.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=changeme
ENABLE_CREDENTIALS_LOGIN=true
KAFKA_BOOTSTRAP_SERVERS=localhost:19092
# Port for Console web.
PORT=4000
ROTOR_AUTH_KEY=${BULKER_TOKEN}
ROTOR_RAW_AUTH_TOKENS=${BULKER_TOKEN}
ROTOR_URL=http://localhost:3401
REPOSITORY_AUTH_TOKEN=service-admin-account:${CONSOLE_TOKEN}
REPOSITORY_BASE_URL=http://localhost:3000/api/admin/export/
REPOSITORY_CACHE_DIR=/tmp/cache

# Allow users to log in with credentials below
ENABLE_CREDENTIALS_LOGIN=true
SEED_USER_EMAIL=<EMAIL>
SEED_USER_PASSWORD=changeme
