{"name": "@jitsu/event-extractors", "version": "0.0.0", "description": "Event data extraction utilities for Jitsu", "main": "dist/index.js", "private": true, "types": "dist/index.d.ts", "scripts": {"build": "pnpm clean && tsc -p .", "compile": "tsc -p .", "clean": "rm -rf ./dist", "test": "jest", "test:watch": "jest --watch"}, "devDependencies": {"tslib": "^2.6.3", "@jitsu/protocols": "workspace:*", "@types/jest": "^29.5.5", "jest": "^29.7.0", "ts-jest": "^29.2.3"}, "dependencies": {"object-path": "^0.11.8"}}