import { RetryError } from "@jitsu/functions-lib";
import type { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { JitsuFunction } from "@jitsu/protocols/functions";

import { TikTokConversionsCredentials } from "../../meta";
import { eventPresetOptions } from "./config";
import { reportWebEvent } from "./reportWebEvent";

/**
 * @see https://github.com/segmentio/action-destinations/tree/main/packages/destination-actions/src/destinations/tiktok-conversions
 */
const TikTokConversionsDestination: JitsuFunction<AnalyticsServerEvent, TikTokConversionsCredentials> = async (
  event,
  ctx
) => {
  const isValidEvent =
    event.type === "page" || (event?.event && eventPresetOptions.includes(event.event) && event.type === "track");

  if (isValidEvent) {
    try {
      return await reportWebEvent(event, ctx);
    } catch (e: any) {
      throw new RetryError(e.message);
    }
  }
};

TikTokConversionsDestination.displayName = "tiktok-conversions-destination";

TikTokConversionsDestination.description =
  "This function sends events to TikTok to measure and optimize TikTok Ads performance.";

export default TikTokConversionsDestination;
