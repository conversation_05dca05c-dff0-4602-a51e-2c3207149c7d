{"name": "@jitsu/core-functions", "version": "0.0.0", "main": "src/index.ts", "types": "src/index.ts", "description": "", "author": "Jitsu Dev Team <<EMAIL>>", "publishConfig": {"access": "public"}, "files": ["src/meta.ts"], "license": "MIT", "private": false, "scripts": {"build": "tsc -p .", "compile": "tsc -p .", "clean": "rm -rf ./dist", "test": "tsc -p . &&  jest --verbose"}, "devDependencies": {"@jitsu/functions-lib": "workspace:*", "@jitsu/protocols": "workspace:*", "@jitsu/sdk-js": "^3.1.5", "@types/jest": "^29.5.12", "@types/lodash": "^4.14.185", "@types/node": "^18.15.3", "express": "^4.21.2", "jest": "^29.7.0", "json5": "^2.1.0", "node-fetch-commonjs": "^3.3.2", "ts-jest": "^29.2.3"}, "dependencies": {"@jitsu/event-extractors": "workspace:*", "@amplitude/ua-parser-js": "^0.7.33", "@clickhouse/client": "^1.10.1", "@hubspot/api-client": "^11.1.0", "@segment/action-destinations": "3.381.0", "@segment/actions-core": "3.150.0", "@mongodb-js/zstd": "^2.0.1", "undici": "^6.21.2", "@segment/destination-subscriptions": "3.37.0", "agentkeepalive": "4.3.0", "axios": "1.8.2", "dayjs": "^1.11.10", "google-ads-api": "^17.1.0-rest-beta", "ioredis": "^5.3.2", "isolated-vm": "5.0.1", "juava": "workspace:*", "lodash": "^4.17.21", "mongodb": "^6.16.0", "node-cache": "^5.1.2", "parse-duration": "^1.1.2", "posthog-node": "^4.2.1", "tslib": "^2.6.3", "xml-js": "^1.6.11", "zod": "^3.23.8", "google-libphonenumber": "^3.2.40", "node-sql-parser": "^5.3.8"}}