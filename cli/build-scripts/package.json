{"name": "jitsu-build-scripts", "version": "0.0.0", "description": "", "author": "Jitsu Dev Team <<EMAIL>>", "publishConfig": {"access": "public"}, "bin": "./bin/jitsu-build-scripts", "license": "MIT", "private": false, "scripts": {"clean": "rm -rf ./dist", "compile": "tsc -p . ", "build": "pnpm compile && webpack", "exec": "ts-node src/index.ts"}, "dependencies": {"boxen": "^7.1.1", "colorette": "^2.0.20", "semver": "^7.5.4", "simple-git": "^3.22.0", "string-width": "^7.0.0", "tslib": "^2.6.3"}, "devDependencies": {"commander": "^11.0.0", "@types/node": "^18.15.3", "@types/semver": "^7.5.6", "@babel/preset-env": "^7.23.2", "@babel/preset-typescript": "^7.23.2", "babel-loader": "^9.1.3", "webpack": "^5.99.5", "webpack-cli": "^6.0.1", "juava": "workspace:*"}}