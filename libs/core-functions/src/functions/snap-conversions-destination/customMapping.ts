import { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { extractEventData } from "@jitsu/event-extractors";

export const customMapping = (event: AnalyticsServerEvent) => {
  try {
    const extractedData = extractEventData(event);
    const { properties = {}, context = {} } = event;
    const { user = {}, order = {}, page = {} } = extractedData;

    const userData = {
      external_id: [user.id ?? user.anonymousId].filter(Boolean),
      email: user.email,
      phone: user.phone,
      gender: user.gender,
      firstName: user.firstName,
      lastName: user.lastName,
      birthday: user.birthday,
      city: user.address?.city,
      state: user.address?.state,
      zip: user.address?.zip ?? user.address?.postalCode,
      country: user.address?.country,
      client_ip_address: user.ip,
      client_user_agent: user.userAgent,
      madid: context.device?.advertisingId,
      idfv: context.device?.id,
      sc_click_id:
        properties.click_id ??
        event.integrations?.["Snap Conversions Api"]?.click_id ??
        extractClickIdFromUrl(page?.url),
      sc_cookie1: event.integrations?.["Snap Conversions Api"]?.uuid_c1,
    };

    const customData = {
      currency: order?.currency,
      num_items: order?.products?.length,
      order_id: order?.orderId ?? order?.id,
      search_string: properties.query,
      sign_up_method: properties.sign_up_method,
      value: Math.max(order?.value ?? 0, order?.total ?? 0, order?.revenue ?? 0),
      checkin_date: properties.checkin_date,
      travel_end: properties.travel_end,
      travel_start: properties.travel_start,
      destination_airport: properties.destination_airport,
      country: properties.country,
      city: properties.city,
      region: properties.region,
      num_adults: properties.num_adults,
      origin_airport: properties.origin_airport,
      num_children: properties.num_children,
      postal_code: properties.postal_code,
    };

    const appData = {
      advertiser_tracking_enabled: context.device?.adTrackingEnabled,
      application_tracking_enabled: context.device?.adTrackingEnabled,
      version: getVersionFromDeviceType(context.device?.type),
      packageName: context.app?.namespace,
      longVersion: context.app?.version,
      osVersion: context.os?.version,
      deviceName: context.device?.model,
      locale: context.locale,
      carrier: context.network?.carrier,
      width: context.screen?.width,
      height: context.screen?.height,
      density: context.screen?.density,
      deviceTimezone: context.timezone,
    };

    const products = (order?.products || []).map(product => ({
      item_id: product.id ?? product.product_id ?? "",
      item_category: product.category,
      brand: product.brand,
    }));

    return {
      event_id: event.messageId,
      event_time: event.timestamp,
      user_data: userData,
      app_data: appData,
      custom_data: customData,
      event_source_url: page?.url,
      products: products.length > 0 ? products : undefined,
      data_processing_options: false,
      data_processing_options_country: 0,
      data_processing_options_state: 0,
    };
  } catch (error) {
    console.error("Error in customMapping:", error);
    throw error;
  }
};

// Helper function to determine version from device type
function getVersionFromDeviceType(deviceType?: string): string | undefined {
  if (deviceType === "ios") return "i2";
  if (deviceType === "android") return "a2";
  return undefined;
}

const extractClickIdFromUrl = (url?: string): string | undefined => {
  if (!url) return undefined;
  const match = url.match(/[?&]ScCid=([^&#]*)/i);
  return match ? match[1] : undefined;
};
