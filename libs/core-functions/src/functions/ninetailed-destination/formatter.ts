import type { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { randomUUID } from "crypto";
import { extractPageData } from "@jitsu/event-extractors";

export function formatEventForNinetailed(event: AnalyticsServerEvent): any {
  const pageData = extractPageData(event);
  const anonymousId = event.anonymousId || randomUUID();

  const result: any = {
    ...event,
    channel: "server",
    messageId: event.messageId || randomUUID(),
    timestamp: formatTimestamp(event.timestamp),
    anonymousId,
  };

  if (event.userId) {
    result.userId = String(event.userId);
  } else {
    result.userId = anonymousId;
  }

  if (event.context?.app) {
    result.context.app = {
      name: String(event.context.app.name || ""),
      version: String(event.context.app.version || ""),
    };
  }

  result.context = {
    ...event.context,
    library: {
      name: "Chord CDP",
      version: "2.0.0",
    },
  };

  if (pageData.url || pageData.path) {
    result.context.page = {
      ...event.context.page,
      path: pageData.path || "/",
      query: pageData.params || {},
      referrer: pageData.referrer || "",
      search: pageData.search || "",
      url: pageData.url || "",
    };
  }

  if (event.type === "page") {
    result.properties = {
      ...event.properties,
      path: pageData.path || "/",
      query: pageData.params || {},
      referrer: pageData.referrer || "",
      search: pageData.search || "",
      url: pageData.url || "",
    };
  }

  return result;
}

function formatTimestamp(timestamp?: string | number | Date): string {
  if (!timestamp) {
    return new Date().toISOString();
  }

  if (typeof timestamp === "string") {
    try {
      return new Date(timestamp).toISOString();
    } catch {
      return new Date().toISOString();
    }
  }

  return new Date(timestamp).toISOString();
}
