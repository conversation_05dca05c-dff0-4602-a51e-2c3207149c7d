import type { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { FullContext } from "@jitsu/protocols/functions";
import type { KlaviyoCredentials } from "../../../meta";
import { API_URL } from "../config";
import { buildHeaders, createProfileData } from "../functions";

export const upsertProfile = async (event: AnalyticsServerEvent, ctx: FullContext) => {
  const profileData = createProfileData(event);
  const props = ctx.props as KlaviyoCredentials;

  const result = await ctx.fetch(`${API_URL}/profiles/`, {
    method: "POST",
    headers: buildHeaders(props.apiKey),
    body: JSON.stringify(profileData),
  });

  if (result.status === 409) {
    const content = await result.json();
    const id = content?.errors[0]?.meta?.duplicate_profile_id;

    if (id) {
      profileData.data.id = id;

      return await ctx.fetch(`${API_URL}/profiles/${id}`, {
        method: "PATCH",
        headers: buildHeaders(props.apiKey),
        body: JSON.stringify(profileData),
      });
    }
  }

  return result;
};
