import { JitsuFunction } from "@jitsu/protocols/functions";
import { RetryError } from "@jitsu/functions-lib";
import type { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { Ga4Credentials } from "../../meta";
import { createFilter, eventTimeSafeMs } from "../lib";
import { StandardProperties } from "./config";
import { Ga4Event, Ga4Request } from "./types";
import {
  getClientId,
  getSessionId,
  getUserProperties,
  removeProperties,
  getItems,
  resolveStandardValue,
  resolvePossibleValue,
  handelizeName,
  getFirebaseAppInstanceId,
} from "./functions";

function pageViewEvent(event: AnalyticsServerEvent): Ga4Event {
  const pageProperties = {
    ...(event.context?.page || {}),
    ...(event.properties || {}),
  };
  return {
    name: "page_view",
    params: {
      page_location: pageProperties.url || "",
      page_referrer: pageProperties.referrer || "",
      page_title: pageProperties.title || "",
      engagement_time_msec: 1,
    },
  };
}

function trackEvent(event: AnalyticsServerEvent): Ga4Event {
  const evp = event.properties || {};
  let params: Record<string, any> = {};
  let name;
  const eventName = event.event || event.type;
  switch (event.event) {
    case "Promotion Clicked":
      name = "select_promotion";
      params.creative_name = evp.creative_name;
      params.creative_slot = evp.creative;
      params.location_id = evp.position;
      params.promotion_id = evp.promotion_id;
      params.promotion_name = evp.promotion_name || evp.name;
      params.items = getItems(event);
      break;
    case "Product List Viewed":
      name = "view_item_list";
      params.item_list_id = evp.list_id;
      params.item_list_name = evp.category;
      params.items = getItems(event);
      break;
    case "Checkout Started":
      name = "begin_checkout";
      params.currency = evp.currency;
      params.value = resolveStandardValue(evp);
      params.coupon = evp.coupon;
      params.items = getItems(event);
      break;
    case "Order Refunded":
      name = "refund";
      params.currency = evp.currency;
      params.transaction_id = evp.order_id;
      params.value = resolvePossibleValue(evp.total, evp.value, evp.revenue);
      params.coupon = evp.coupon;
      params.shipping = evp.shipping;
      params.affiliation = evp.affiliation;
      params.tax = evp.tax;
      params.items = getItems(event);
      break;
    case "Product Added":
      name = "add_to_cart";
      params.currency = evp.currency;
      params.value = resolveStandardValue(evp);
      params.items = getItems(event);
      break;
    case "Payment Info Entered":
      name = "add_payment_info";
      params.currency = evp.currency;
      params.value = resolveStandardValue(evp);
      params.coupon = evp.coupon;
      params.payment_type = evp.payment_method;
      params.items = getItems(event);
      break;
    case "Product Added to Wishlist":
      name = "add_to_wishlist";
      params.currency = evp.currency;
      params.value = resolveStandardValue(evp);
      params.items = getItems(event);
      break;
    case "Product Viewed":
      name = "view_item";
      params.currency = evp.currency;
      params.value = resolveStandardValue(evp);
      params.items = getItems(event);
      break;
    case "Signed Up":
      name = "sign_up";
      params.method = evp.type || evp.method;
      break;
    case "Order Completed":
      name = "purchase";
      params.currency = evp.currency;
      params.transaction_id = evp.order_id;
      params.value = resolvePossibleValue(evp.total, evp.value, evp.revenue);
      params.coupon = evp.coupon;
      params.shipping = evp.shipping;
      params.affiliation = evp.affiliation;
      params.tax = evp.tax;
      params.items = getItems(event);
      break;
    case "Promotion Viewed":
      name = "view_promotion";
      params.creative_name = evp.creative_name;
      params.creative_slot = evp.creative;
      params.location_id = evp.position;
      params.promotion_id = evp.promotion_id;
      params.promotion_name = evp.promotion_name || evp.name;
      params.items = getItems(event);
      break;
    case "Cart Viewed":
      name = "view_cart";
      params.currency = evp.currency;
      params.value = resolveStandardValue(evp);
      params.items = getItems(event);
      break;
    case "Signed In":
      name = "login";
      params.method = evp.type || evp.method;
      break;
    case "Product Removed":
      name = "remove_from_cart";
      params.currency = evp.currency;
      params.value = resolveStandardValue(evp);
      params.items = getItems(event);
      break;
    case "Products Searched":
      name = "search";
      params.search_term = evp.query;
      break;
    case "Product Clicked":
      name = "select_item";
      params.item_list_id = evp.list_id;
      params.item_list_name = evp.category;
      params.items = getItems(event);
      break;
    case "Generate Lead":
      name = "generate_lead";
      params.currency = evp.currency;
      params.value = resolveStandardValue(evp);
      params = { ...evp };
      params = removeProperties(params, [...StandardProperties, "engagement_time_msec", "value", "currency"]);
      params.engagment_time_msec = evp.engagement_time_msec || 1;
      break;
    default:
      name = handelizeName(eventName);
      params = { ...evp };
      params = removeProperties(params, StandardProperties);
      params.currency = evp.currency;
      params.value = resolveStandardValue(evp);
      break;
  }
  params.engagement_time_msec = 1;
  return {
    name,
    params,
  };
}

const Ga4Destination: JitsuFunction<AnalyticsServerEvent, Ga4Credentials> = async (event, ctx) => {
  if (typeof ctx.props.events !== "undefined") {
    const filter = createFilter(ctx.props.events || "");
    if (!filter(event.type, event.event)) {
      return;
    }
  }
  let gaRequest: Ga4Request | undefined = undefined;
  try {
    const clientId = getClientId(event);
    const sessionId = getSessionId(event, ctx.props.measurementId);
    const firebaseAppInstanceId = ctx.props.firebaseAppId ? ctx.props.firebaseAppId : getFirebaseAppInstanceId(event);
    const measurementId = ctx.props.measurementId || "";
    let query = `api_secret=${ctx.props.apiSecret}`;
    let idPart = {} as any;
    if (measurementId.match(/^\d:\d+:\w+:\w+$/)) {
      if (!firebaseAppInstanceId) {
        ctx.log.info(`Ga4: no app instance id found for event ID: ${event.messageId}`);
        return;
      }
      idPart.app_instance_id = firebaseAppInstanceId;
      query += `&firebase_app_id=${measurementId}`;
    } else {
      if (!clientId) {
        ctx.log.info(`Ga4: no client_id found for event ID: ${event.messageId}`);
        return;
      }
      if (!measurementId.match(/^G-\w+$/)) {
        ctx.log.warn(`Ga4: measurement_id is not in the correct format: ${measurementId}`);
      }
      idPart.client_id = clientId;
      query += `&measurement_id=${measurementId}`;
    }

    const userProperties = getUserProperties(event);
    const events: Ga4Event[] = [];

    switch (event.type) {
      case "page":
        events.push(pageViewEvent(event));
        break;
      case "track":
      case "alias":
      case "group":
      case "identify":
        events.push(trackEvent(event));
    }
    if (events.length === 0) {
      ctx.log.info(`Ga4: no GA4 event is mapped for event type: ${event.type} ID: ${event.messageId}`);
      return;
    }

    const baseUrl = ctx.props.url ?? "https://www.google-analytics.com/mp/collect";

    const url = `${baseUrl}?${query}`;

    gaRequest = {
      ...idPart,
      user_id: event.userId,
      timestamp_micros: eventTimeSafeMs(event) * 1000,
      user_properties: userProperties,
      events: sessionId ? events.map(e => ({ ...e, params: { ...e.params, session_id: sessionId } })) : events,
    };

    const result = await ctx.fetch(url, {
      method: "POST",
      body: JSON.stringify(gaRequest),
    });

    // if (ctx.props.validationMode) {
    //   ctx.log.info(`Ga4:${JSON.stringify(gaRequest)} --> ${result.status}: ${await result.text()}`);
    // } else
    if (result.status !== 200 && result.status !== 204) {
      throw new Error(`Ga4:${JSON.stringify(gaRequest)} --> ${result.status} ${await result.text()}`);
    } else {
      ctx.log.debug(`Ga4: ${result.status} ${await result.text()}`);
    }
  } catch (e: any) {
    throw new RetryError(`Failed to send request to Ga4: ${JSON.stringify(gaRequest)}: ${e?.message}`);
  }
};

Ga4Destination.displayName = "ga4-destination";

Ga4Destination.description =
  "This functions covers jitsu events and sends them to Google Analytics 4 using The Google Analytics Measurement Protocol";

export default Ga4Destination;
