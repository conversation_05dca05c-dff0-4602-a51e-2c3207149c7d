{"name": "root", "version": "0.0.0", "private": false, "scripts": {"build-scripts": "pnpm --filter ./cli/build-scripts run exec", "format:check": "prettier --ignore-unknown --check --config ./.prettierrc.json --ignore-path ./.prettierignore $(git diff --name-only --diff-filter d | xargs)", "format:check:all": "prettier --check --config ./.prettierrc.json --ignore-path ./.prettierignore .", "format": "prettier --ignore-unknown --write --config ./.prettierrc.json --ignore-path ./.prettierignore $(git diff --name-only --diff-filter d | xargs)", "format:all": "prettier --write --config ./.prettierrc.json --ignore-path ./.prettierignore .", "tool:hash": "turbo run tool:hash --", "ci:no-test": "pnpm format:check:all && pnpm lint && pnpm build", "ci:all": "pnpm ci:no-test && pnpm test", "prepare": "husky install", "pre-commit": "pnpm format:check", "build": "turbo run build", "ee-api:dev": "dotenv -e .env.local -- turbo run ee-api:dev", "console:dev": "dotenv -e .env.local -- turbo run console:dev", "console:storybook": "dotenv -e .env.local -- turbo run console:storybook", "rotor:dev": "dotenv -e .env.local -- turbo run rotor:dev", "console:start": "dotenv -e .env.local -- turbo run console:start", "console:db-prepare": "dotenv -e .env.local -- pnpm run --filter=console db:update-schema", "console:db-prepare-force": "dotenv -e .env.local -- pnpm run --filter=console db:update-schema-force", "dev": "dotenv -e .env.local -- turbo run dev", "test": "turbo run test", "jitsu-cli": "turbo run build --filter=jitsu-cli && node --enable-source-maps core/jitsu-cli/lib/index.js", "clean:turbo": "rm -rf `find . -type d -name .turbo`", "factory-reset": "pnpm clean ; pnpm clean:turbo ; rm -rf `find . -type d -name node_modules`", "clean": "turbo run clean || exit 0", "lint": "turbo run lint --", "release": "pnpm build && pnpm test && monorel --filter ./types/protocols --filter ./cli/jitsu-cli --filter ./libs/functions --filter ./libs/jitsu-js --filter ./libs/jitsu-react --npm-tag latest --git-tag 'jitsu-js-libs-v{version}' --push-tag", "release:build-scripts": "pnpm build --filter ./cli/build-scripts && monorel --filter ./cli/build-scripts --npm-tag latest --version 0.0.4-dev.{rev}.{time} --publish --git-tag 'jitsu-build-scripts-v{version}'", "release:canary": "monorel --filter ./types/protocols --filter ./cli/jitsu-cli --filter ./libs/functions --filter ./libs/jitsu-js --filter ./libs/jitsu-react --version '1.10.1-canary.{rev}.{time}' --npm-tag canary --git-tag 'jitsu-js-libs-canary-v{version}' --push-tag"}, "devDependencies": {"@playwright/test": "1.39.0", "dotenv-cli": "^6.0.0", "husky": "^8.0.3", "monorel": "0.4.2", "prettier": "^2.8.7", "ts-node": "^10.9.2", "turbo": "~1.2.16", "typescript": "^5.6.3"}, "engines": {"yarn": ">=1000", "pnpm": ">=8", "npm": ">=1000", "node": "18.x"}, "pnpm": {"overrides": {"zstd-napi": "^0.0.10", "nth-check": "2.1.1", "nanoid": "3.3.8", "postcss": "^8.4.47", "elliptic": "^6.6.1", "cross-spawn": "^7.0.6", "path-to-regexp": "^0.1.12"}, "patchedDependencies": {"@segment/action-destinations": "patches/@segment__action-destinations.patch"}}, "workspaces": ["examples/*", "e2e", "types/*", "webapps/*", "services/*", "cli/*", "libs/*"]}