import { Ga4Item } from "./types";
import type { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { ReservedUserProperties } from "./config";

export function removeProperties(properties: Record<string, any>, toRemove: string[]): Record<string, any> {
  for (const key of toRemove) {
    delete properties[key];
  }
  return properties;
}

export function getItems(event: AnalyticsServerEvent): Ga4Item[] {
  if (!event.properties) return [];

  let items: Ga4Item[] = [];
  if (Array.isArray(event.properties.products)) {
    items = event.properties.products.map(getItem).filter(item => item != undefined) as Ga4Item[];
  } else {
    const item = getItem(event.properties);
    if (item) items.push(item);
  }
  return items;
}

function getItem(product: any): Ga4Item | undefined {
  if (!product.product_id || !product.name) return undefined;
  return {
    item_id: product.product_id,
    item_name: product.name,
    affiliation: product.affiliation,
    coupon: product.coupon,
    creative_name: product.creative_name,
    creative_slot: product.creative_slot,
    currency: product.currency,
    discount: product.discount,
    index: product.position,
    item_brand: product.brand,
    item_category: product.category,
    item_category2: product.category2,
    item_category3: product.category3,
    item_category4: product.category4,
    item_category5: product.category5,
    item_list_id: product.list_id,
    item_list_name: product.list_name,
    item_variant: product.variant,
    location_id: product.location_id,
    price: product.price,
    promotion_id: product.promotion_id,
    promotion_name: product.promotion_name,
    quantity: product.quantity,
  };
}

export function getUserProperties(event: AnalyticsServerEvent): Record<string, any> {
  let userProperties: Record<string, any> = {};
  // const ua = parser(event.context?.userAgent);
  // userProperties["platform"] = { value: "web" };
  // userProperties["os"] = { value: ua.os?.name };
  // userProperties["os_version"] = { value: ua.os?.version };
  // userProperties["browser"] = { value: `${ua.browser?.name};${ua.browser?.version}` };
  // userProperties["device_category"] = { value: ua.device?.type };
  // userProperties["device_model"] = { value: ua.device?.model };
  // userProperties["device_brand"] = { value: ua.device?.vendor };
  // userProperties["language"] = { value: event.context?.locale };
  //
  // for (const [key, prop] of Object.entries(userProperties)) {
  //   //remove empty values
  //   if (prop.value == undefined) delete userProperties[key];
  // }

  for (const [key, value] of Object.entries((event.type == "identify" ? event.traits : event.context?.traits) || {})) {
    if (
      !ReservedUserProperties.includes(key) &&
      !key.startsWith("google_") &&
      !key.startsWith("ga_") &&
      !key.startsWith("firebase_")
    ) {
      userProperties[key] = { value };
    }
  }
  return userProperties;
}

export function getClientId(event: AnalyticsServerEvent): string | undefined {
  return event.context?.clientIds?.ga4?.clientId || event.anonymousId || undefined;
}

export function getFirebaseAppInstanceId(event: AnalyticsServerEvent): string | undefined {
  return event.context?.clientIds?.firebase?.appInstanceId;
}

export function getSessionId(event: AnalyticsServerEvent, measurementId: string): string | undefined {
  return event.context?.clientIds?.ga4?.sessionIds?.[measurementId.replace("G-", "")];
}

/**
 * Adjusts a given name string by replacing all non-alphanumeric characters
 * (except underscores) with underscores, converting the string to lowercase,
 * and truncating it to a maximum length of 40 characters.
 *
 * @param name - The input string to be adjusted.
 * @returns The adjusted string with the specified transformations applied.
 */
export function handelizeName(name: string): string {
  return name
    .replace(/[^a-zA-Z0-9_]/g, "_")
    .toLowerCase()
    .substring(0, 40);
}

// `0`is falsy so we need to explicitly check for it
export function resolvePossibleValue(...possibleValues: any[]): any {
  for (const value of possibleValues) {
    if (typeof value !== "undefined" && value !== null) {
      return value;
    }
  }

  return possibleValues.at(-1);
}

// typically values are resolved in the following order, however there are
// a few exceptions.
export function resolveStandardValue(evp: any) {
  return resolvePossibleValue(evp?.value, evp?.total, evp?.revenue);
}
