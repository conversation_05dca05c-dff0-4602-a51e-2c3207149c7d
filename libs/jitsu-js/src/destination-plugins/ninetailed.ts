import { AnalyticsClientEvent } from "@jitsu/protocols/analytics";
import { applyFilters, CommonDestinationCredentials, InternalPlugin } from "./index";

export type NinetailedDestinationCredentials = {
  sendPageEvents?: boolean;
} & CommonDestinationCredentials;

declare global {
  interface Window {
    ninetailed?: any;
    __chordNinetailedQueue?: ((nt: any) => void | Promise<void>)[];
    __chordNinetailedState?: "fresh" | "initializing" | "loaded";
    __chordNinetailedPoller?: number;
  }
}

function getNinetailedState(): "fresh" | "initializing" | "loaded" {
  return window.__chordNinetailedState || "fresh";
}

function setNinetailedState(s: "fresh" | "initializing" | "loaded") {
  window.__chordNinetailedState = s;
}

function getNinetailedQueue(): ((nt: any) => void | Promise<void>)[] {
  return window.__chordNinetailedQueue || (window.__chordNinetailedQueue = []);
}

function sanitizeProperties(properties: any): any {
  if (!properties || typeof properties !== "object") {
    return properties;
  }

  const sanitized: any = {};

  function flatten(obj: any, prefix = ""): void {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const value = obj[key];
        const newKey = prefix ? `${prefix}.${key}` : key;

        if (value === null || value === undefined) {
          sanitized[newKey] = value;
        } else if (typeof value === "string" || typeof value === "number" || typeof value === "boolean") {
          sanitized[newKey] = value;
        } else if (Array.isArray(value)) {
          // Convert arrays to comma-separated strings or handle simple arrays
          if (value.every(item => typeof item === "string" || typeof item === "number" || typeof item === "boolean")) {
            sanitized[newKey] = value.join(", ");
          } else {
            sanitized[newKey] = JSON.stringify(value);
          }
        } else if (typeof value === "object") {
          // Recursively flatten nested objects
          try {
            flatten(value, newKey);
          } catch (e) {
            // If flattening fails, stringify the object
            sanitized[newKey] = JSON.stringify(value);
          }
        } else {
          // For any other type, convert to string
          sanitized[newKey] = String(value);
        }
      }
    }
  }

  try {
    flatten(properties);
    return sanitized;
  } catch (e) {}
}

function flushNinetailedQueue(nt: any) {
  const queue = getNinetailedQueue();
  while (queue.length > 0) {
    const action = queue.shift();
    try {
      const result = action!(nt);
      if (result && typeof result.catch === "function") {
        result.catch((e: any) => {});
      }
    } catch (e: any) {}
  }
}

function handleEvent(config: NinetailedDestinationCredentials, payload: AnalyticsClientEvent) {
  return (ninetailed: any) => {
    try {
      const { type } = payload;
      switch (type) {
        case "page":
          if (config.sendPageEvents !== false) {
            const { properties } = payload;
            if (properties) {
              const pageProps = sanitizeProperties(properties);
              if (!pageProps) {
                return;
              }
              if (pageProps.url === undefined) {
                pageProps.url = window.location.href;
              }
              ninetailed.page(pageProps);
            } else {
              ninetailed.page();
            }
          }
          break;
        case "track":
          const { event, properties } = payload;
          if (!event) {
            console.error("Event name is required for track calls");
            return;
          }
          const sanitizedProperties = sanitizeProperties(properties || {});
          ninetailed.track(event, sanitizedProperties);
          break;
        case "identify":
          const { userId, traits } = payload;
          const sanitizedTraits = sanitizeProperties(traits || {});
          ninetailed.identify(userId, sanitizedTraits);
          break;
        default:
          break;
      }
    } catch (e: any) {}
  };
}

function initNinetailedIfNeeded() {
  if (getNinetailedState() !== "fresh") {
    return;
  }
  setNinetailedState("initializing");

  if (window.ninetailed) {
    setNinetailedState("loaded");
    flushNinetailedQueue(window.ninetailed);
    return;
  }

  if (window.__chordNinetailedPoller) {
    return;
  }

  // Poll for the Ninetailed SDK to become available
  window.__chordNinetailedPoller = setInterval(() => {
    if (window.ninetailed) {
      clearInterval(window.__chordNinetailedPoller);
      window.__chordNinetailedPoller = undefined;
      setNinetailedState("loaded");
      flushNinetailedQueue(window.ninetailed);
    }
  }, 100);

  // Clean up the poller after 30 seconds to prevent memory leaks
  setTimeout(() => {
    if (window.__chordNinetailedPoller) {
      clearInterval(window.__chordNinetailedPoller);
      window.__chordNinetailedPoller = undefined;
      if (getNinetailedState() === "initializing") {
        console.warn(
          "Ninetailed SDK was not found after 30 seconds. Events have been queued but may not be processed."
        );
      }
    }
  }, 30000);
}

export const ninetailedPlugin: InternalPlugin<NinetailedDestinationCredentials> = {
  id: "ninetailed",

  async handle(config, payload: AnalyticsClientEvent) {
    if (!applyFilters(payload, config, "ninetailed")) {
      return;
    }

    initNinetailedIfNeeded();

    try {
      const action = handleEvent(config, payload);
      getNinetailedQueue().push(action);
    } catch (e: any) {}

    if (getNinetailedState() === "loaded" && window.ninetailed) {
      flushNinetailedQueue(window.ninetailed);
    }
  },
};
