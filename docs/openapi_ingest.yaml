---
openapi: 3.0.3
info:
  title: API for Chord CDP Ingest service
  description: API for sending events to Chord CDP, via Ingest service
  license:
    name: Proprietary
    url: https://www.chordcommerce.com/terms
  version: "1.0"
servers:
  - url: https://staging.cdp.ingest.chord.co
    description: Staging Console
  - url: https://production.cdp.ingest.chord.co
    description: Production
security:
  - WriteKeyAuth: []
paths:
  /health:
    get:
      operationId: getHealth
      summary: Health check
      responses:
        "200":
          $ref: "#/components/responses/ResponseHealthCheckSuccess"
        "403":
          $ref: "#/components/responses/ResponseUnauthorized"
  /api/s/track:
    post:
      operationId: createEvent
      summary: Send a frontend track event
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/TrackPayload"
      responses:
        "200":
          $ref: "#/components/responses/ResponseSuccess"
        "403":
          $ref: "#/components/responses/ResponseUnauthorized"
  /api/s/page:
    post:
      operationId: createFrontendPageEvent
      summary: Send a frontend page event
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PagePayload"
      responses:
        "200":
          $ref: "#/components/responses/ResponseSuccess"
        "403":
          $ref: "#/components/responses/ResponseUnauthorized"
  /api/s/identify:
    post:
      operationId: createFrontendIdentifyEvent
      summary: Send a frontend identify event
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IdentifyPayload"
      responses:
        "200":
          $ref: "#/components/responses/ResponseSuccess"
        "403":
          $ref: "#/components/responses/ResponseUnauthorized"
  /api/s/s2s/track:
    post:
      operationId: createBackendTrackEvnet
      summary: Send a backend track event
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/TrackPayload"
      responses:
        "200":
          $ref: "#/components/responses/ResponseSuccess"
        "403":
          $ref: "#/components/responses/ResponseUnauthorized"
  /api/s/s2s/page:
    post:
      operationId: createBackendPageEvent
      summary: Send a backend page event
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PagePayload"
      responses:
        "200":
          $ref: "#/components/responses/ResponseSuccess"
        "403":
          $ref: "#/components/responses/ResponseUnauthorized"
  /api/s/s2s/identify:
    post:
      operationId: createBackendIdentifyEvent
      summary: Send a backend identify event
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IdentifyPayload"
      responses:
        "200":
          $ref: "#/components/responses/ResponseSuccess"
        "403":
          $ref: "#/components/responses/ResponseUnauthorized"
components:
  securitySchemes:
    WriteKeyAuth:
      type: apiKey
      in: header
      name: X-Write-Key
  schemas:
    IdentifyPayload:
      type: object
      properties:
        type:
          type: string
          description: The type of event (i.e. Track, Identify)
      required: [type]
    PagePayload:
      type: object
      properties:
        type:
          type: string
          description: The type of event (i.e. Track, Identify)
      required: [type]
    TrackPayload:
      type: object
      properties:
        type:
          type: string
          description: The type of event (i.e. Track, Identify)
      required: [type]
    ResponseSuccess:
      type: object
      properties:
        ok:
          type: string
          example: ok
    ResponseHealthSuccess:
      type: object
      properties:
        status:
          type: string
          example: pass
  responses:
    ResponseHealthCheckSuccess:
      description: Health check successful
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ResponseHealthSuccess"
    ResponseSuccess:
      description: Event received successfully
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ResponseSuccess"
    ResponseUnauthorized:
      description: Invalid or missing write key
