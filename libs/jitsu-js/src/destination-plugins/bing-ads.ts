import { AnalyticsClientEvent } from "@jitsu/protocols/analytics";
import { applyFilters, CommonDestinationCredentials, InternalPlugin } from "./index";

// Define the specific credentials needed for Bing Ads
export type BingAdsDestinationCredentials = {
  tagId?: string;
} & CommonDestinationCredentials;

// Declare the global types Bing Ads script adds to the window
declare global {
  interface Window {
    UET?: any; // Bing UET constructor
    uetq?: any[] | any; // Bing event queue or initialized object
  }
}

// Define the Bing Ads plugin
export const bingAdsPlugin: InternalPlugin<BingAdsDestinationCredentials> = {
  id: "bing-ads", // Unique ID for this plugin

  // The main handler function for incoming Jitsu events
  async handle(config, payload: AnalyticsClientEvent) {
    // Apply any configured event filters
    if (!applyFilters(payload, config, "bing-ads")) {
      return;
    }

    // Ensure the Bing Ads script is loaded and initialized
    await initBingAdsIfNeeded(config);

    // Check if initialization succeeded and uetq is available
    if (getBingAdsState() !== "loaded" || !window.uetq || typeof window.uetq.push !== "function") {
      console.warn("Bing Ads UET object (uetq) not available. Skipping event.");
      return;
    }

    const { type, properties, event } = payload;

    switch (type) {
      case "page":
        // Track page views
        window.uetq.push("pageLoad");
        break;
      case "track":
        // Track custom events
        const bingEvent: Record<string, any> = {
          ea: "track",
          el: event,
        };
        // Add category if available
        if (properties?.category) {
          bingEvent.ec = properties.category;
        }
        // Add revenue/value if available
        if (properties?.revenue !== undefined && properties?.revenue !== null) {
          bingEvent.gv = properties.revenue;
        }

        // Push the custom event object to Bing
        window.uetq.push(bingEvent);
        break;
    }
  },
};

// Function to load and initialize the Bing Ads script if it hasn't been already
async function initBingAdsIfNeeded(config: BingAdsDestinationCredentials) {
  if (getBingAdsState() !== "fresh") {
    return; // Already loading, loaded, or failed
  }

  if (!config.tagId) {
    console.warn("Bing Ads Tag ID (tagId) is not configured.");
    setBingAdsState("failed");
    return;
  }

  setBingAdsState("loading");

  try {
    (function (w, d, t, r, u) {
      var f, n, i;
      (w[u] = w[u] || []),
        (f = function () {
          var o = { ti: config.tagId, enableAutoSpaTracking: true, q: undefined };
          (o.q = w[u]), (w[u] = new window.UET(o)), w[u].push("pageLoad");
        }),
        (n = d.createElement(t)),
        (n.src = r),
        (n.async = 1),
        (n.onload = n.onreadystatechange =
          function () {
            var s = this.readyState;
            (s && s !== "loaded" && s !== "complete") || (f(), (n.onload = n.onreadystatechange = null));
          }),
        (i = d.getElementsByTagName(t)[0]),
        i.parentNode.insertBefore(n, i);
      setBingAdsState("loaded");
    })(window, document, "script", "//bat.bing.com/bat.js", "uetq");
  } catch (e: any) {
    console.warn(`Bing Ads (tagId=${config.tagId}) init failed: ${e.message}`, e);
    setBingAdsState("failed");
  }
}

type BingAdsState = "fresh" | "loading" | "loaded" | "failed";

function getBingAdsState(): BingAdsState {
  return window["__chordCDPBingAdsState"] || "fresh";
}

function setBingAdsState(s: BingAdsState) {
  window["__chordCDPBingAdsState"] = s;
}
