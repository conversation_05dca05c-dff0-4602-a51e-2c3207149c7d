import type { AnalyticsServerEvent, AnalyticsClientEvent } from "@jitsu/protocols/analytics";

/**
 * Checks if an event should be processed based on user consent preferences
 * @param event Analytics event containing consent information
 * @param requiredCategories Array of category IDs required for this destination
 * @returns boolean indicating if the event should be processed
 */
export function shouldProcessEvent(
  event: AnalyticsServerEvent | AnalyticsClientEvent,
  requiredCategories: string[]
): boolean {
  // If no consent information is available, default to allowing the event
  if (!event.context?.consent?.categoryPreferences) {
    return true;
  }

  // If no required categories, allow the event
  if (!requiredCategories || requiredCategories.length === 0) {
    return true;
  }

  const userConsent = event.context.consent.categoryPreferences;

  // Check if all required consent categories are accepted by the user
  return requiredCategories.every(category => {
    // If the category exists in user preferences and is true, allow it
    return userConsent[category] === true;
  });
}
