import type { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { FullContext } from "@jitsu/protocols/functions";
import type { KlaviyoCredentials } from "../../../meta";
import { API_URL } from "../config";
import { buildHeaders, createEventData } from "../functions";

export const trackEvent = async (event: AnalyticsServerEvent, ctx: FullContext) => {
  const eventData = createEventData(event);
  const props = ctx.props as KlaviyoCredentials;

  const result = await ctx.fetch(`${API_URL}/events`, {
    method: "POST",
    headers: buildHeaders(props.apiKey),
    body: JSON.stringify(eventData),
  });

  if (result.status !== 202) {
    throw new Error(`
      <PERSON>laviyo Post failed for event ${event.event}
      HTTP Request URL: ${API_URL}/events
      ---
      ${eventData && `Payload: ${JSON.stringify(eventData)}`}
      ---
      Reuslt Status: ${result.status}
      ---
      Reuslt Text: ${await result.text()}
    `);
  }

  ctx.log.debug(`Klaviyo: ${result.status} ${await result.text()}`);

  return result;
};
