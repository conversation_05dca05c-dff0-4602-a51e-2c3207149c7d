import { PhoneNumberUtil, PhoneNumberFormat } from "google-libphonenumber";

const phoneUtil = PhoneNumberUtil.getInstance();

export function validateAndConvertPhoneNumber(phone?: string, countryCode?: string): string | undefined | null {
  if (!phone) return;

  const e164Regex = /^\+[1-9]\d{1,14}$/;

  // Check if the phone number is already in E.164 format
  if (e164Regex.test(phone)) {
    return phone;
  }

  // If phone number is not in E.164 format, attempt to convert it using the country code
  if (countryCode) {
    try {
      const parsedPhone = phoneUtil.parse(phone, countryCode);
      const isValid = phoneUtil.isValidNumberForRegion(parsedPhone, countryCode);

      if (!isValid) {
        return null;
      }

      return phoneUtil.format(parsedPhone, PhoneNumberFormat.E164);
    } catch (error) {
      return null;
    }
  }

  return null;
}

export function processPhoneNumber(initialPhoneNumber?: string, country_code?: string): string {
  const phone_number = validateAndConvertPhoneNumber(initialPhoneNumber, country_code);

  if (!phone_number) {
    throw new Error(`${initialPhoneNumber} is not a valid phone number and cannot be converted to E.164 format.`);
  }

  return phone_number;
}
