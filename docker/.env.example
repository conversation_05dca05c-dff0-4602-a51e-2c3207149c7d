#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 	REQUIRED PARAMETERS, PLEASE READ INSTRUCTIONS THOROUGHLY
# See https://docs.jitsu.com/self-hosting/quick-start/ for more details
#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

# Password and login of an *initial* admin user of Jitsu UI. After initial login, you must change your password!
# You can keep those variable empty if you are configuring Jitsu UI via Github auth (see below). In this case first
# authorized user will be granted a full access to Jitsu UI.
SEED_USER_EMAIL=<EMAIL>
SEED_USER_PASSWORD=changeme

# Domain name of your Jitsu server where events will be sent. E.g. https://jitsu.mycompany.com or http://localhost:8080
JITSU_INGEST_PUBLIC_URL=http://localhost:8080

# Public URL of your Jitsu UI. E.g. https://jitsu.mycompany.com or http://localhost:3000
JITSU_PUBLIC_URL=http://localhost:3000

#### Secrets ###
# It is highly recommended to generate random values for those variables.
# You can use `openssl rand -hex 32` to generate random values.

CONSOLE_TOKEN=changeme
BULKER_TOKEN=changeme
SYNCCTL_TOKEN=changeme
POSTGRES_PASSWORD=changeme

#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 	OPTIONAL PARAMETERS
#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

JITSU_UI_PORT=3000
JITSU_INGEST_PORT=8080

# If you want to have more than one user for Jitsu UI, this could be done via delegating auth to github.
# You need to create a new Github OAuth app and set those variables accordingly.
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=

#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 	Connectors specific
#++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SYNCS_ENABLED=false

# sync controller is running using host network to access 'minikube' w/o problems.
EXTERNAL_SYNCS_PORT=3043

#postgres host how it is reachable from k8s cluster
EXTERNAL_POSTGRES_HOST=host.minikube.internal
EXTERNAL_POSTGRES_PORT=5432

SYNCCTL_KUBERNETES_CLIENT_CONFIG=
# to use non-default k8s context
SYNCCTL_KUBERNETES_CONTEXT=
# For syncs scheduling
# google service account key
GOOGLE_SCHEDULER_KEY=
# for Google Ads connector
GOOGLE_ADS_DEVELOPER_TOKEN=
