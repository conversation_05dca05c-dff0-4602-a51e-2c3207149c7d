---
name: Issue template
about: Template for creating issues, both bugs and feature requests
title: ''
labels: ''
assignees: <PERSON><PERSON><PERSON><PERSON><PERSON>

---
```
┌─────────────────────────────────────────────────────────────────────────────────────────────────┐
│ IMPORTANT: GitHub is for discussing issues of self-hosting Jitsu. Please make sure that your    │
│ issue can be reproduced for self-hosting environment. If you're experiencing problem with your  │
│ Jitsu Cloud account, <NAME_EMAIL>                                           │
└─────────────────────────────────────────────────────────────────────────────────────────────────┘

Please delete this block before submitting the issue
```

## Summary

<!-- Please put a summary of the issue with a clear context and motivation -->

## System configuration and versions

<!-- 
Is it Jitsu Classic or Jitsu Next? What is the version of Docker images you're using? 
-->

## Artifacts (logs, etc)

<!-- 
If you're reporting a problem, please attach logs. 
If possible, attach all log entries, not only lines with error message. Make sure to remove or mask any sensitive information such as api keys, IP addresses etc 
-->
