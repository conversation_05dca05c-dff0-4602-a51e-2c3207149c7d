import { JitsuFunction } from "@jitsu/protocols/functions";
import { RetryError } from "@jitsu/functions-lib";
import type { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { BrazeCredentials } from "../../meta";
import { updateUserProfile, identifyUser, trackEvent, trackPurchase } from "./functions";
import { endpoints, ORDER_COMPLETED_EVENT } from "./config";
import { HttpRequest } from "./types";

const BrazeDestination: JitsuFunction<AnalyticsServerEvent, BrazeCredentials> = async (event, ctx) => {
  const endpoint = endpoints[ctx.props.endpoint];
  if (!endpoint) {
    throw new Error(`Unknown endpoint ${ctx.props.endpoint}`);
  }
  try {
    let httpRequests: HttpRequest[] = [];
    const headers = {
      "Content-type": "application/json",
      Authorization: `Bearer ${ctx.props.apiKey}`,
    };
    const url = `${endpoint}/users/track`;
    try {
      if (event.type === "identify") {
        httpRequests.push({
          url,
          payload: updateUserProfile(event, ctx),
          headers,
        });
        const identify = identifyUser(event, ctx);
        if (identify) {
          httpRequests.push({
            url: `${endpoint}/users/identify`,
            payload: identify,
            headers,
          });
        }
      } else if (event.type === "track" && event.event != ORDER_COMPLETED_EVENT) {
        httpRequests.push({
          url,
          payload: trackEvent(event, ctx),
          headers,
        });
      } else if (event.type === "track" && event.event === ORDER_COMPLETED_EVENT) {
        httpRequests.push({
          url,
          payload: trackPurchase(event, ctx),
          headers,
        });
      } else if ((event.type === "page" || event.type === "screen") && ctx.props.sendPageEvents) {
        const track = { ...event };
        track.event = event.type;
        const props = { ...event.properties };
        if (event.name) {
          props[`${event.type}_name`] = event.name;
        }
        if (event.category) {
          props[`${event.type}_category`] = event.category;
        }
        track.properties = props;
        httpRequests.push({
          url,
          payload: trackEvent(track, ctx),
          headers,
        });
      }
    } catch (e: any) {
      ctx.log.error(e);
      return false;
    }

    for (const httpRequest of httpRequests) {
      if (httpRequest.payload) {
        const method = httpRequest.method || "POST";
        const result = await ctx.fetch(httpRequest.url, {
          method,
          headers: httpRequest.headers,
          ...(httpRequest.payload ? { body: JSON.stringify(httpRequest.payload) } : {}),
        });
        if (result.status !== 200 && result.status !== 201) {
          throw new Error(
            `
            Braze ${method} failed for event ${event.event}
            ---
            HTTP Request URL: ${httpRequest.url}
            ---
            ${httpRequest.payload && `Payload: ${JSON.stringify(httpRequest.payload)}`}
            ---
            Reuslt Status: ${result.status}
            ---
            Reuslt Text: ${await result.text()}
            `
          );
        } else {
          ctx.log.debug(`Braze ${method} ${httpRequest.url}: ${result.status} ${await result.text()}`);
        }
      }
    }
  } catch (e: any) {
    throw new RetryError(e.message);
  }
};

BrazeDestination.displayName = "braze-destination";

BrazeDestination.description = "This functions covers jitsu events and sends them to Braze";

export default BrazeDestination;
