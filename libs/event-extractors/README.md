
# This is AI generated for reference purposes

# Guide: Implementing Custom Mapping for Destination Plugins

## Overview

Custom mapping allows users to transform events and their properties before sending them to destination platforms. This guide explains how to implement custom mapping support in any Jitsu destination plugin using the generic custom mapping framework.

## Core Concepts

### 1. Custom Mapping Structure

The custom mapping system uses a generic structure that can be adapted to any destination:

```typescript
interface GenericEventMapping {
  sourceEvent: string;      // The incoming event name or type
  targetEvent: string;      // The destination platform's event name
  parameters?: Array<{      // Property mappings
    sourceProperty: string; // Path to source property (supports dot notation)
    targetProperty: string; // Destination property name
  }>;
}
```

### 2. Implementation Steps

#### Step 1: Define Your Destination's Mapping Configuration

First, define the configuration structure for your destination plugin:

```typescript
export type MyDestinationCredentials = {
  // ... other destination-specific config ...
  
  // Add custom mapping configuration
  customMappings?: Array<{
    sourceEvent: string;
    targetEvent: string;
    parameters?: Array<{
      sourceProperty: string;
      targetProperty: string;
    }>;
  }>;
} & CommonDestinationCredentials;
```

#### Step 2: Import Required Functions

```typescript
import { 
  getGenericEventMapping, 
  processArrayMappings,
  type GenericEventMapping 
} from "@jitsu/event-extractors";
```

#### Step 3: Implement Custom Mapping in Your Plugin

Here's a template for implementing custom mapping in your destination plugin:

```typescript
import { InternalPlugin } from "../index";
import { AnalyticsClientEvent } from "@jitsu/protocols/analytics";
import { getGenericEventMapping, processArrayMappings } from "@jitsu/event-extractors";

export const myDestinationPlugin: InternalPlugin<MyDestinationCredentials> = {
  id: "my-destination",
  
  async handle(config, payload: AnalyticsClientEvent) {
    // Convert destination-specific mappings to generic format
    const genericMappings: GenericEventMapping[] = config.customMappings?.map(m => ({
      sourceEvent: m.sourceEvent,
      targetEvent: m.targetEvent,
      parameters: m.parameters?.map(p => ({
        sourceProperty: p.sourceProperty,
        targetProperty: p.targetProperty,
      })),
    })) || [];

    // Apply custom mapping based on event type
    const mappingOptions = {
      matchOnEventName: payload.type === "track", // Match on event name for track events
    };
    
    const customMapping = getGenericEventMapping(payload, genericMappings, mappingOptions);
    
    // Prepare the final event
    let finalEventName = payload.event || payload.type;
    let finalParameters = { ...payload.properties };
    
    if (customMapping) {
      // Use custom event name if provided
      if (customMapping.targetEvent) {
        finalEventName = customMapping.targetEvent;
      }
      
      // Apply parameter mappings
      finalParameters = {
        ...finalParameters,
        ...customMapping.parameters,
      };
      
      // Handle array mappings if present
      if (customMapping.itemMappings && finalParameters.items) {
        finalParameters.items = processArrayMappings(
          finalParameters.items, 
          customMapping.itemMappings
        );
      }
    }
    
    // Send to destination
    await sendToDestination(finalEventName, finalParameters);
  },
};
```

## Advanced Features

### 1. Array Property Mapping

The custom mapping system supports mapping properties from arrays using the `[*]` wildcard syntax:

```typescript
// Example configuration
const mapping = {
  sourceEvent: "Order Completed",
  targetEvent: "purchase",
  parameters: [
    // Map array elements
    { sourceProperty: "properties.products[*].sku", targetProperty: "item_ids" },
    { sourceProperty: "properties.products[*].price", targetProperty: "item_prices" },
  ]
};
```

### 2. Handling Different Event Types

For non-track events (page, identify, group), match on the event type instead:

```typescript
// For page events
const pageMapping = getGenericEventMapping(
  { ...payload, event: "page" }, 
  genericMappings, 
  { matchOnEventName: false }
);

// For identify events  
const identifyMapping = getGenericEventMapping(
  { ...payload, event: "identify" },
  genericMappings,
  { matchOnEventName: false }
);
```

### 3. Nested Property Access

The system supports deep property access using dot notation:

```typescript
{ sourceProperty: "properties.order.items[0].product.name", targetProperty: "first_item_name" }
{ sourceProperty: "context.traits.address.city", targetProperty: "user_city" }
```

## Complete Example: Facebook Pixel Plugin

Here's a complete example of implementing custom mapping for a Facebook Pixel destination:

```typescript:libs/jitsu-js/src/destination-plugins/facebook-pixel/index.ts
import { InternalPlugin, applyFilters, CommonDestinationCredentials } from "../index";
import { AnalyticsClientEvent } from "@jitsu/protocols/analytics";
import { getGenericEventMapping, processArrayMappings } from "@jitsu/event-extractors";
import { loadScript } from "../../script-loader";

export type FacebookPixelCredentials = {
  pixelId: string;
  customMappings?: Array<{
    sourceEvent: string;
    facebookEvent: string;
    parameters?: Array<{
      sourceProperty: string;
      facebookProperty: string;
    }>;
  }>;
} & CommonDestinationCredentials;

export const facebookPixelPlugin: InternalPlugin<FacebookPixelCredentials> = {
  id: "facebook-pixel",
  
  async handle(config, payload: AnalyticsClientEvent) {
    if (!applyFilters(payload, config, "facebook-pixel")) {
      return;
    }

    // Initialize Facebook Pixel if needed
    await initFacebookPixel(config.pixelId);

    // Convert to generic mapping format
    const genericMappings = config.customMappings?.map(m => ({
      sourceEvent: m.sourceEvent,
      targetEvent: m.facebookEvent,
      parameters: m.parameters?.map(p => ({
        sourceProperty: p.sourceProperty,
        targetProperty: p.facebookProperty,
      })),
    })) || [];

    // Default event mappings for Facebook
    const defaultMappings = {
      "Order Completed": { event: "Purchase", requiredParams: ["value", "currency"] },
      "Product Added": { event: "AddToCart", requiredParams: ["value", "currency"] },
      "Checkout Started": { event: "InitiateCheckout", requiredParams: ["value"] },
    };

    let eventName: string;
    let parameters: Record<string, any> = {};

    // Check for custom mapping
    const customMapping = getGenericEventMapping(
      payload, 
      genericMappings, 
      { matchOnEventName: payload.type === "track" }
    );

    if (customMapping) {
      eventName = customMapping.targetEvent || payload.event || "track";
      parameters = { ...customMapping.parameters };

      // Handle array mappings for product items
      if (customMapping.itemMappings && payload.properties?.products) {
        const items = processArrayMappings(
          payload.properties.products,
          customMapping.itemMappings
        );
        parameters.contents = items;
      }
    } else if (payload.type === "track" && defaultMappings[payload.event]) {
      // Use default mapping
      const mapping = defaultMappings[payload.event];
      eventName = mapping.event;
      
      // Map standard properties
      parameters = {
        value: payload.properties?.revenue || payload.properties?.value,
        currency: payload.properties?.currency || "USD",
        content_ids: payload.properties?.products?.map(p => p.product_id),
        content_type: "product",
        num_items: payload.properties?.products?.length,
      };
    } else {
      // No mapping found, send as custom event
      eventName = payload.event || payload.type;
      parameters = payload.properties || {};
    }

    // Send to Facebook Pixel
    window.fbq("track", eventName, parameters);
  },
};

async function initFacebookPixel(pixelId: string) {
  if (window.fbq) return;
  
  // Facebook Pixel initialization code
  !function(f,b,e,v,n,t,s){
    if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)
  }(window,document,'script','https://connect.facebook.net/en_US/fbevents.js');
  
  window.fbq('init', pixelId);
}
```

## Best Practices

### 1. Provide Default Mappings

Include sensible default mappings for common events while allowing custom overrides:

```typescript
const DEFAULT_EVENT_MAPPINGS = {
  "Order Completed": "purchase",
  "Product Viewed": "view_item",
  "Product Added": "add_to_cart",
};

// Check custom mapping first, fall back to defaults
const eventName = customMapping?.targetEvent || 
                  DEFAULT_EVENT_MAPPINGS[payload.event] || 
                  payload.event;
```

### 2. Validate Required Parameters

Ensure required parameters for the destination are present:

```typescript
function validateRequiredParams(event: string, params: Record<string, any>) {
  const requirements = {
    purchase: ["transaction_id", "value", "currency"],
    add_to_cart: ["value", "items"],
  };
  
  const required = requirements[event];
  if (required) {
    const missing = required.filter(param => !params[param]);
    if (missing.length > 0) {
      console.warn(`Missing required parameters for ${event}: ${missing.join(", ")}`);
    }
  }
}
```

### 3. Handle Edge Cases

Always handle cases where custom mapping might not be found:

```typescript
if (!customMapping && payload.type === "track") {
  // Fallback behavior for unmapped events
  const eventName = sanitizeEventName(payload.event);
  const parameters = {
    ...payload.properties,
    event_category: "custom",
  };
  sendEvent(eventName, parameters);
}
```

### 4. Support Different Event Types

Implement mapping for all relevant event types:

```typescript
switch (payload.type) {
  case "page":
    handlePageEvent(payload, genericMappings);
    break;
  case "track":
    handleTrackEvent(payload, genericMappings);
    break;
  case "identify":
    handleIdentifyEvent(payload, genericMappings);
    break;
}
```

## Configuration Example

Here's how users would configure custom mappings:

```json
{
  "destinationType": "facebook-pixel",
  "credentials": {
    "pixelId": "123456789",
    "customMappings": [
      {
        "sourceEvent": "Product Purchased",
        "facebookEvent": "Purchase",
        "parameters": [
          {
            "sourceProperty": "properties.revenue",
            "facebookProperty": "value"
          },
          {
            "sourceProperty": "properties.products[*].product_id",
            "facebookProperty": "content_ids"
          },
          {
            "sourceProperty": "properties.products[*].price",
            "facebookProperty": "contents[*].price"
          }
        ]
      }
    ]
  }
}
```

## Testing Your Implementation

Always test your custom mapping implementation:

```typescript
describe("Custom Mapping", () => {
  it("should apply custom event mapping", () => {
    const event = {
      type: "track",
      event: "My Custom Event",
      properties: { value: 100 }
    };
    
    const mappings = [{
      sourceEvent: "My Custom Event",
      targetEvent: "custom_conversion",
      parameters: [{
        sourceProperty: "properties.value",
        targetProperty: "conversion_value"
      }]
    }];
    
    const result = getGenericEventMapping(event, mappings);
    expect(result.targetEvent).toBe("custom_conversion");
    expect(result.parameters.conversion_value).toBe(100);
  });
});
```

This guide provides a comprehensive approach to implementing custom mapping in any Jitsu destination plugin, enabling flexible event transformation to match each platform's requirements.