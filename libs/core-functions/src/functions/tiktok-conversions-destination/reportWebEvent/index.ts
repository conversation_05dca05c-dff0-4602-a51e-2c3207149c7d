import type { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { FullContext } from "@jitsu/protocols/functions";
import type { TikTokConversionsCredentials } from "../../../meta";
import { API_URL } from "../config";
import { buildHeaders, createEventData } from "../functions";

/**
 * Sends tracking event data to TikTok Conversions API
 * @param event Analytics event to be processed
 * @param ctx Function execution context
 * @returns API response from TikTok
 */
export const reportWebEvent = async (event: AnalyticsServerEvent, ctx: FullContext) => {
  try {
    const eventData = createEventData(event, ctx);
    const props = ctx.props as TikTokConversionsCredentials;

    const response = await ctx.fetch(`${API_URL}/event/track/`, {
      method: "POST",
      headers: buildHeaders(props.accessToken),
      body: JSON.stringify(eventData),
    });

    const responseData = await response.json();

    if (response.status !== 200) {
      const errorMsg = `TikTok API error: ${response.status} ${JSON.stringify(responseData)}`;
      ctx.log.error(errorMsg, {
        statusCode: response.status,
        rawEventData: event,
        sentData: eventData,
        responseData,
      });
      throw new Error(errorMsg);
    }

    ctx.log.debug("TikTok event tracking successful", {
      eventId: event.messageId,
      responseStatus: response.status,
      rawEventData: event,
      sentData: eventData,
      responseData,
    });

    return response;
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : String(err);
    ctx.log.error("TikTok event tracking failed", { error: errorMessage });
    throw err;
  }
};
