import { RetryError } from "@jitsu/functions-lib";
import type { AnalyticsServerEvent } from "@jitsu/protocols/analytics";
import { JitsuFunction } from "@jitsu/protocols/functions";
import { customEvent } from "./customEvents";
import { AttentiveCredentials } from "../../meta";
import { ATTENTIVE_MISSING_IDENTIFIER } from "./functions";
import { ErrorWithCode } from "../../lib/error-types";

// @see https://github.com/segmentio/action-destinations/tree/main/packages/destination-actions/src/destinations/attentive
const AttentiveDestination: JitsuFunction<AnalyticsServerEvent, AttentiveCredentials> = async (event, ctx) => {
  try {
    if (event.type === "track") {
      return await customEvent(event, ctx);
    }
  } catch (e: any) {
    if (e instanceof ErrorWithCode && e.code === ATTENTIVE_MISSING_IDENTIFIER) {
      ctx.log.info(`Skipping event due to ${e.message}`);
      return;
    }
    throw new RetryError(e.message);
  }
};

AttentiveDestination.displayName = "attentive-destination";

AttentiveDestination.description = "This function sends events to Attentive";

export default AttentiveDestination;
