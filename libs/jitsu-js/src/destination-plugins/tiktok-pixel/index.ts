import { AnalyticsClientEvent } from "@jitsu/protocols/analytics";
import { applyFilters, CommonDestinationCredentials, InternalPlugin } from "../index";
import { formatAddress, formatPhone, formatString, createEventProperties } from "./functions/lib";
import { extractEventData } from "@jitsu/event-extractors";

type TikTokPixelCredentials = {
  pixelCode: string;
  ldu?: boolean;
  autoPageView?: boolean;
} & CommonDestinationCredentials;

declare global {
  interface Window {
    TiktokAnalyticsObject?: any;
    ttq?: any;
  }
}

export const tiktokPixelPlugin: InternalPlugin<TikTokPixelCredentials> = {
  id: "tiktok-pixel",
  async handle(config, payload: AnalyticsClientEvent) {
    if (!applyFilters(payload, config, "tiktok-pixel")) {
      return;
    }

    await initTikTokPixelIfNeeded(config);

    const { properties } = payload;
    const eventData = extractEventData(payload);
    const { user, eventName, eventType } = eventData;

    if (user.id || user.anonymousId || user.email || user.phone) {
      window.ttq.identify({
        email: formatString(user.email),
        phone_number: formatPhone(user.phone),
        external_id: user.id || user.anonymousId,
        first_name: formatString(user.firstName),
        last_name: formatString(user.lastName),
        city: formatAddress(user.address?.city),
        state: formatAddress(user.address?.state),
        country: formatAddress(user.address?.country),
        zip_code: formatString(user.address?.postalCode || user.address?.zip),
      });
    }

    switch (eventType) {
      case "page":
        // this is handled by config.autoPageView control flow - see below
        break;

      case "identify":
        // Identify is handled above
        break;

      case "track":
        switch (eventName) {
          case "Order Completed":
            window.ttq
              .instance(config.pixelCode)
              .track("CompletePayment", createEventProperties(eventData, properties), { event_id: payload.messageId });
            break;

          case "Checkout Started":
            window.ttq
              .instance(config.pixelCode)
              .track("InitiateCheckout", createEventProperties(eventData, properties), { event_id: payload.messageId });
            break;

          case "Product Added":
            window.ttq
              .instance(config.pixelCode)
              .track("AddToCart", createEventProperties(eventData, properties), { event_id: payload.messageId });
            break;

          case "Product Viewed":
            window.ttq
              .instance(config.pixelCode)
              .track("ViewContent", createEventProperties(eventData, properties), { event_id: payload.messageId });
            break;

          case "Products Searched":
            window.ttq
              .instance(config.pixelCode)
              .track("Search", createEventProperties(eventData, properties), { event_id: payload.messageId });
            break;

          case "Payment Info Entered":
            window.ttq
              .instance(config.pixelCode)
              .track("AddPaymentInfo", createEventProperties(eventData, properties), { event_id: payload.messageId });
            break;

          case "Product Added to Wishlist":
            window.ttq
              .instance(config.pixelCode)
              .track("AddToWishlist", createEventProperties(eventData, properties), { event_id: payload.messageId });
            break;

          case "Signed Up":
            window.ttq
              .instance(config.pixelCode)
              .track("CompleteRegistration", createEventProperties(eventData, properties), {
                event_id: payload.messageId,
              });
            break;

          case "Form Submitted":
            window.ttq
              .instance(config.pixelCode)
              .track("SubmitForm", createEventProperties(eventData, properties), { event_id: payload.messageId });
            break;

          case "Callback Started":
            window.ttq
              .instance(config.pixelCode)
              .track("Contact", createEventProperties(eventData, properties), { event_id: payload.messageId });
            break;

          case "Subscription Created":
            window.ttq
              .instance(config.pixelCode)
              .track("Subscribe", createEventProperties(eventData, properties), { event_id: payload.messageId });
            break;

          case "Product Clicked":
            window.ttq
              .instance(config.pixelCode)
              .track("ClickButton", createEventProperties(eventData, properties), { event_id: payload.messageId });
            break;

          case "Order Placed":
            window.ttq
              .instance(config.pixelCode)
              .track("PlaceAnOrder", createEventProperties(eventData, properties), { event_id: payload.messageId });
            break;

          case "Download Link Clicked":
            window.ttq
              .instance(config.pixelCode)
              .track("Download", createEventProperties(eventData, properties), { event_id: payload.messageId });
            break;

          default:
            window.ttq
              .instance(config.pixelCode)
              .track(eventName, createEventProperties(eventData, properties), { event_id: payload.messageId });
            break;
        }
        break;
    }
  },
};

type TikTokPixelState = "fresh" | "loading" | "loaded" | "failed";

function getTikTokPixelState(): TikTokPixelState {
  return window["__chordCDPTikTokPixelState"] || "fresh";
}

function setTikTokPixelState(s: TikTokPixelState) {
  window["__chordCDPTikTokPixelState"] = s;
}

async function initTikTokPixelIfNeeded(config: TikTokPixelCredentials): Promise<void> {
  if (getTikTokPixelState() !== "fresh") {
    return;
  }

  setTikTokPixelState("loading");

  try {
    (function (w, d, t) {
      w.TiktokAnalyticsObject = t;
      var ttq = (w[t] = w[t] || []);
      (ttq.methods = [
        "page",
        "track",
        "identify",
        "instances",
        "debug",
        "on",
        "off",
        "once",
        "ready",
        "alias",
        "group",
        "enableCookie",
        "disableCookie",
        "holdConsent",
        "revokeConsent",
        "grantConsent",
      ]),
        (ttq.setAndDefer = function (t, e) {
          t[e] = function () {
            t.push([e].concat(Array.prototype.slice.call(arguments, 0)));
          };
        });
      for (var i = 0; i < ttq.methods.length; i++) ttq.setAndDefer(ttq, ttq.methods[i]);
      (ttq.instance = function (t) {
        for (var e = ttq._i[t] || [], n = 0; n < ttq.methods.length; n++) ttq.setAndDefer(e, ttq.methods[n]);
        return e;
      }),
        (ttq.load = function (e, n) {
          var r = "https://analytics.tiktok.com/i18n/pixel/events.js";
          (ttq._i = ttq._i || {}),
            (ttq._i[e] = []),
            (ttq._i[e]._u = r),
            (ttq._t = ttq._t || {}),
            (ttq._t[e] = +new Date()),
            (ttq._o = ttq._o || {}),
            (ttq._o[e] = n || {}),
            (ttq._partner = ttq._partner || "Chord");

          n = document.createElement("script");
          (n.type = "text/javascript"), (n.async = !0), (n.src = r + "?sdkid=" + e + "&lib=" + t);
          e = document.getElementsByTagName("script")[0];
          e.parentNode.insertBefore(n, e);
        });

      ttq.load(config.pixelCode, {
        limited_data_use: config.ldu ? config.ldu : false,
      });

      if (config.autoPageView === undefined || config.autoPageView === true) {
        ttq.instance(config.pixelCode).page();
      }

      setTikTokPixelState("loaded");
    })(window, document, "ttq");
  } catch (error) {
    console.error("Failed to initialize TikTok Pixel:", error);
    setTikTokPixelState("failed");
  }
}
