import * as Sentry from "@sentry/node";

Sentry.init({
  dsn: "https://<EMAIL>/4509073830117376",
  enabled: process.env.NODE_ENV === "production", // The docker image is built with NODE_ENV=production
  environment: process.env.CDP_AWS_ENV || "unknown", // ECS container environment name

  // Set sampling rate for profiling - this is evaluated only once per SDK.init
  profileSessionSampleRate: 1.0,
});
