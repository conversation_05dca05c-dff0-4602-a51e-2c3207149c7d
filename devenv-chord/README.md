# Destination development environment

This directory contains a development environment for creating and modifying
destinations.

The primary difference between this environment and the one found in `../docker`
is that this does not contain the `console` or `rotor` services, which need to
be run locally instead. It also excludes other non-essential services. There is
a similar environment in `../devenv`, but it was not created by the <PERSON><PERSON> team
and it does not contain what is needed for this purpose.

## Requirements

You'll first need to:

1. Install and start Docker
2. Install Node.js 18 (`nvm use 18` or `asdf install nodejs 18.20.4 && asdf set nodejs 18.20.4`)
3. Install `pnpm` v9 (the project is _not_ compatible with v10). There are a couple of different ways to accomplish this; choose the one that works for you:
  a. `corepack enable pnpm`
  b. `npm install -g pnpm@9` to install to your global NodeJS.
  c. `npm install pnpm@9` to install to the local NodeJS. Note you might encounter an error installing `pnpm` using
     `npm` because the `package.json` file was built using `pnpm` and contains blocks that are not supported in `npm`,
     specifically the `workspaces` array. So, remove the `workspaces` array from `package.json`, install `pnpm@9`,
     then reinsert it.

### Environment variables

You'll need two `.env` files:

* one within this directory (`./.env`). This one will be used by `./docker-compose.yml`.
* one at the root of the repository (`../.env.local`). This one will be used by
  the local `console` and `rotor` services. It mimics the environment variables
  for those services within `../docker/docker-compose.yml`.

```
cd devenv-chord
cp .env.example .env
cp .env.local.example ../.env.local
```

You may want to change the `BULKER_TOKEN`, `CLICKHOUSE_PASSWORD`, `CONSOLE_TOKEN` and `POSTGRES_PASSWORD` values using `openssl rand -hex 32`. Though sensible defualts are included in the example files.
The variables found in both, such as `BULKER_TOKEN`, must continue to match.

## Usage

From the root of the repository...

### 1. Install all dependencies

```
pnpm install

# If you installed pnpm to the local project, prefix with npx:
npx pnpm install
```

If you encounter this error, you can ignore it, as it doesn't seem to affect the app build (but maybe one of the destinations):

```
 ERR_PNPM_PATCH_NOT_APPLIED  The following patches were not applied: @segment/action-destinations
```

### 2. Build the applications

```
pnpm build

# If you installed pnpm to the local project, prefix with npx:
npx pnpm install
```

### 3. Create a directory for SSL certificates & Generate self-signed certificates:
This is to set up HTTPS for the ingest service running in Docker. We're using a reverse proxy with Let's Encrypt for SSL certificates.

`mkdir -p devenv-chord/ssl`

then run

```
cd devenv-chord/ssl && openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout key.pem -out cert.pem -subj "/CN=localhost"
```

### 4. Create and start the Docker environment

_If you have not previously downloaded our `ingest` image, read
[this](#ingest-image-download) first._

While Docker is running:

```
docker-compose -f devenv-chord/docker-compose.yml up -d
```

_The `ingest` and `bulker` services will appear to continuously restart in
Docker Desktop until the `console` service is run locally. This is expected._

_If you would ever like to destroy all services and start over, run
`docker-compose -f devenv-chord/docker-compose.yml down`._

### 4. Prepare the console database

```
pnpm console:db-prepare
```

_This is only needed after first creating the `postgres` service with Docker._

### 5. Run the console app

From a separate window:

```
pnpm console:dev
```

### 6. Run the rotor app

From a separate window:

```
pnpm rotor:dev
```

### 7. Log into the console

Navigate to `http://localhost:3000` in the browser and log in with the
credentials found in the environment variables.

_If the Live Events page does not work and the CLI mentions the `events_log`
table, try visiting `http://localhost:3000/api/admin/events-log-init?token=<CONSOLE_TOKEN>` in the
browser. It's an endpoint that should create that table, where _<console_token>_ is the `CONSOLE_TOKEN` specified in 
`.env.local`.

## Ingest image download

This is necessary in order to pull our bespoke image of the `ingest` service.

_If you have any trouble along the way, try asking ChatGPT. :)_

### Configuring SSO for AWS CLI

Bethany wrote a detailed document for [configuring SSO for AWS CLI](https://www.notion.so/Authenticating-via-SSO-in-the-AWS-CLI-cd4a1eae9d71471a86d90314ec0c9c9e) in Notion.

### Pull the image into Docker

Authenticate to the AWS `chord-development` account on the command line (refer to instructions above).

Once logged in to the AWS CLI via SSO, authenticate Docker to an Amazon private
registry _(replacing `DEFAULT_CLIENT_REGION` and `ACCOUNT_ID`)_:

```
# Declare env vars for conveniences
export DEFAULT_CLIENT_REGION=us-east-1
export ACCOUNT_ID=************ # chord-development account ID

aws ecr get-login-password --region $DEFAULT_CLIENT_REGION | docker login --username AWS --password-stdin $ACCOUNT_ID.dkr.ecr.$DEFAULT_CLIENT_REGION.amazonaws.com
```

The CLI should report `Login Succeeded`.

Next, pull the image:

```
docker pull $ACCOUNT_ID.dkr.ecr.$DEFAULT_CLIENT_REGION.amazonaws.com/cdp/development/ingest:latest
```

You should see the following success message in the terminal:

```
Status: Downloaded newer image for ACCOUNT_ID.dkr.ecr.DEFAULT_CLIENT_REGION.amazonaws.com/cdp/development/ingest:latest
ACCOUNT_ID.dkr.ecr.DEFAULT_CLIENT_REGION.amazonaws.com/cdp/development/ingest:latest
```

The image will appear in your Docker Desktop app. You can now proceed with
[starting the Docker environment](#3-create-and-start-the-docker-environment).
