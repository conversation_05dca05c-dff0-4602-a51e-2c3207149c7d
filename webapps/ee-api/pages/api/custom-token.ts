import { NextApiRequest, NextApiResponse } from "next";
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../lib/route-helpers";
import { createCustomToken } from "../../lib/firebase-auth";

const handler = async function handler(req: NextApiRequest, res: NextApiResponse) {
  res.setHeader("Access-Control-Allow-Origin", req.headers.origin || "*");
  res.setHeader("Access-Control-Allow-Methods", "*");
  res.setHeader("Access-Control-Allow-Headers", "authorization, content-type, baggage, sentry-trace");
  res.setHeader("Access-Control-Allow-Credentials", "true");
  if (req.method === "OPTIONS") {
    res.status(200).end();
    return;
  }
  const customToken = await createCustomToken(req);
  if (customToken) {
    return { customToken: customToken };
  } else {
    return {};
  }
};

export default with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(handler);
